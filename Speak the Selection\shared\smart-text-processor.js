// Smart Text Processing for TTS Optimization
// Enhances text quality for better TTS output across all providers
// Maintains 100% backward compatibility

class SmartTextProcessor {
    static cache = new Map();
    static cacheTimeout = 300000; // 5 minutes

    static preprocess(text, provider = 'google') {
        // Check cache first
        const cacheKey = `${provider}-${text.substring(0, 100)}`;
        const cached = this.cache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.result;
        }

        const result = {
            original: text,
            cleaned: this.cleanText(text),
            optimized: this.optimizeForTTS(text, provider),
            metadata: this.analyzeText(text)
        };

        // Cache the result
        this.cache.set(cacheKey, {
            result,
            timestamp: Date.now()
        });

        // Clean old cache entries
        if (this.cache.size > 100) {
            this.cleanCache();
        }

        return result;
    }

    static cleanText(text) {
        return text
            .replace(/\s+/g, ' ')                     // Normalize whitespace
            .replace(/([.!?])\s*([A-Z])/g, '$1 $2')    // Fix sentence spacing
            .replace(/\u00A0/g, ' ')                   // Replace non-breaking spaces
            .replace(/[\u2018\u2019]/g, "'")           // Replace smart quotes
            .replace(/[\u201C\u201D]/g, '"')           // Replace smart double quotes
            .replace(/\u2013|\u2014/g, '-')           // Replace em/en dashes
            .replace(/\u2026/g, '...')                // Replace ellipsis
            .trim();
    }

    static optimizeForTTS(text, provider) {
        let optimized = text;

        // Universal optimizations for all providers
        const universalOptimizations = [
            // URLs and emails
            { pattern: /\b(\w+)\.com\b/g, replacement: '$1 dot com' },
            { pattern: /\b([\w._%+-]+)@([\w.-]+\.[A-Z]{2,})\b/gi, replacement: 'email address' },

            // Numbers and percentages
            { pattern: /\b(\d+)%\b/g, replacement: '$1 percent' },
            { pattern: /\$(\d+)/g, replacement: '$1 dollars' },
            {
                pattern: /\b(\d{1,3}(,\d{3})*)\b/g, replacement: (match) => {
                    return match.replace(/,/g, ' ');
                }
            },

            // Dates
            { pattern: /\b(\d{4})-(\d{2})-(\d{2})\b/g, replacement: 'date $2 $3 $1' },
            { pattern: /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/g, replacement: 'date $1 $2 $3' },

            // Times
            { pattern: /\b(\d{1,2}):(\d{2})\s*(AM|PM)\b/gi, replacement: '$1 $2 $3' },

            // Abbreviations
            { pattern: /\betc\./g, replacement: 'etcetera' },
            { pattern: /\be\.g\./g, replacement: 'for example' },
            { pattern: /\bi\.e\./g, replacement: 'that is' },
            { pattern: /\bvs\./g, replacement: 'versus' },

            // Units
            { pattern: /\b(\d+)\s*ft\b/g, replacement: '$1 feet' },
            { pattern: /\b(\d+)\s*in\b/g, replacement: '$1 inches' },
            { pattern: /\b(\d+)\s*lb\b/g, replacement: '$1 pounds' },
            { pattern: /\b(\d+)\s*kg\b/g, replacement: '$1 kilograms' },

            // Social media
            { pattern: /#(\w+)/g, replacement: 'hashtag $1' },
            { pattern: /@(\w+)/g, replacement: 'at $1' }
        ];

        // Apply universal optimizations
        universalOptimizations.forEach(({ pattern, replacement }) => {
            if (typeof replacement === 'function') {
                optimized = optimized.replace(pattern, replacement);
            } else {
                optimized = optimized.replace(pattern, replacement);
            }
        });

        // Provider-specific optimizations
        switch (provider) {
            case 'google':
                optimized = this.optimizeForGoogle(optimized);
                break;
            case 'elevenlabs':
                optimized = this.optimizeForElevenLabs(optimized);
                break;
            case 'hume':
                optimized = this.optimizeForHume(optimized);
                break;
            case 'straico':
                optimized = this.optimizeForStraico(optimized);
                break;
        }

        return optimized;
    }

    static optimizeForGoogle(text) {
        // Google TTS specific optimizations
        return text
            .replace(/([a-z])([A-Z])/g, '$1. $2')    // Add periods for camelCase
            .replace(/\b([A-Z]{2,})\b/g, (match) => {
                // Spell out acronyms
                return match.split('').join(' ');
            });
    }

    static optimizeForElevenLabs(text) {
        // ElevenLabs handles natural speech well, minimal optimization needed
        return text;
    }

    static optimizeForHume(text) {
        // Hume AI focuses on emotion, preserve emotional cues
        return text;
    }

    static optimizeForStraico(text) {
        // Straico specific optimizations
        return text;
    }

    static analyzeText(text) {
        const words = text.split(/\s+/);
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

        return {
            wordCount: words.length,
            sentenceCount: sentences.length,
            characterCount: text.length,
            avgWordsPerSentence: Math.round(words.length / sentences.length) || 0,
            avgWordLength: Math.round(words.reduce((sum, word) => sum + word.length, 0) / words.length) || 0,
            complexity: this.calculateComplexity(text),
            estimatedDuration: this.estimateReadingTime(text),
            language: this.detectLanguage(text),
            hasSpecialContent: this.detectSpecialContent(text)
        };
    }

    static calculateComplexity(text) {
        let score = 0;
        const words = text.split(/\s+/);
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

        // Word length factor
        const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
        if (avgWordLength > 6) score += 1;
        if (avgWordLength > 8) score += 1;

        // Sentence length factor
        const avgSentenceLength = text.length / sentences.length;
        if (avgSentenceLength > 100) score += 1;
        if (avgSentenceLength > 200) score += 1;

        // Special characters
        const specialChars = (text.match(/[^a-zA-Z0-9\s.,!?]/g) || []).length;
        if (specialChars > text.length * 0.05) score += 1;

        // Technical content
        const technicalPatterns = [
            /\b\w+\.\w+\b/,  // domain.extension
            /\b[A-Z]{2,}\b/, // ACRONYMS
            /\b\d+[a-zA-Z]+/, // mixed numbers/letters
            /[{}()\[\]]/     // brackets
        ];

        technicalPatterns.forEach(pattern => {
            if (pattern.test(text)) score += 1;
        });

        // Return complexity level
        if (score <= 2) return 'simple';
        if (score <= 4) return 'moderate';
        return 'complex';
    }

    static estimateReadingTime(text) {
        // Average reading speed: 200 words per minute
        const words = text.split(/\s+/).length;
        const minutes = words / 200;
        return Math.max(1, Math.round(minutes));
    }

    static detectLanguage(text) {
        // Simple language detection (English by default)
        const patterns = {
            spanish: /\b(el|la|los|las|un|una|y|de|en|a|es|por|para|con|se|que|no|te|le|da|su|por|son|como|pero|muy|todo|esta|ser|estar|tener|hacer|poder|decir|año|vez|tiempo|casa|hombre|mujer|vida|mano|parte|lugar|trabajo|semana|ojo|caso|cosa|estado|forma|grupo|numero|agua|dinero)\b/gi,
            french: /\b(le|la|les|un|une|de|du|des|et|à|il|elle|en|avec|par|pour|sur|dans|avoir|être|faire|dire|aller|voir|savoir|pouvoir|falloir|vouloir|venir|prendre|donner|parler|aimer|temps|année|jour|homme|femme|enfant|main|chose|lieu|moment|question|heure|fois|travail|argent|ami)\b/gi,
            german: /\b(der|die|das|ein|eine|und|zu|in|mit|von|für|auf|an|bei|durch|über|unter|nach|vor|zwischen|während|wegen|trotz|ohne|gegen|um|bis|seit|haben|sein|werden|können|müssen|sollen|wollen|dürfen|mögen|lassen|gehen|kommen|machen|sagen|geben|bekommen|finden|nehmen|bringen|Jahr|Zeit|Mann|Frau|Kind|Hand|Haus|Leben|Arbeit|Geld|Freund)\b/gi
        };

        for (const [lang, pattern] of Object.entries(patterns)) {
            const matches = (text.match(pattern) || []).length;
            if (matches > text.split(/\s+/).length * 0.1) {
                return lang;
            }
        }

        return 'english';
    }

    static detectSpecialContent(text) {
        const patterns = {
            hasCode: /```|\bfunction\b|\bclass\b|\bconst\b|\blet\b|\bvar\b|\breturn\b/i,
            hasMarkup: /<\/?[\w+][^>]*>/,
            hasUrls: /https?:\/\/[^\s]+/,
            hasEmails: /\b[\w._%+-]+@[\w.-]+\.[A-Z]{2,}\b/i,
            hasPhones: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/,
            hasMath: /\b\d+\s*[+\-*/=]\s*\d+\b/,
            hasQuotes: /["'"']/
        };

        const detected = {};
        for (const [type, pattern] of Object.entries(patterns)) {
            detected[type] = pattern.test(text);
        }

        return detected;
    }

    static cleanCache() {
        const now = Date.now();
        for (const [key, { timestamp }] of this.cache) {
            if (now - timestamp > this.cacheTimeout) {
                this.cache.delete(key);
            }
        }
    }

    static clearCache() {
        this.cache.clear();
    }

    static getStats() {
        return {
            cacheSize: this.cache.size,
            cacheTimeout: this.cacheTimeout
        };
    }
}

// Context-aware text processing
class ContextualProcessor {
    static processBasedOnContext(text, pageContext = {}) {
        const context = this.analyzePageContext(pageContext);

        switch (context.type) {
            case 'article':
                return this.processArticle(text, context);
            case 'documentation':
                return this.processDocumentation(text, context);
            case 'code':
                return this.processCode(text, context);
            case 'email':
                return this.processEmail(text, context);
            default:
                return SmartTextProcessor.preprocess(text);
        }
    }

    static analyzePageContext(pageContext) {
        const hostname = pageContext.hostname || window.location.hostname;
        const pathname = pageContext.pathname || window.location.pathname;
        const title = pageContext.title || document.title;

        return {
            type: this.detectContentType({ hostname, pathname, title }),
            domain: this.categorizeWebsite(hostname),
            structure: this.analyzePageStructure(),
            metadata: { hostname, pathname, title }
        };
    }

    static detectContentType({ hostname, pathname, title }) {
        // Documentation sites
        if (hostname.includes('docs.') || pathname.includes('/docs/') || title.includes('Documentation')) {
            return 'documentation';
        }

        // Code repositories
        if (hostname.includes('github.') || hostname.includes('gitlab.') || pathname.includes('.js') || pathname.includes('.py')) {
            return 'code';
        }

        // Email interfaces
        if (hostname.includes('mail.') || hostname.includes('gmail.') || title.includes('Mail')) {
            return 'email';
        }

        // News and articles
        if (hostname.includes('news.') || hostname.includes('blog.') || pathname.includes('/article/')) {
            return 'article';
        }

        return 'generic';
    }

    static categorizeWebsite(hostname) {
        const categories = {
            social: ['twitter.com', 'facebook.com', 'linkedin.com', 'instagram.com'],
            news: ['cnn.com', 'bbc.com', 'reuters.com', 'ap.org'],
            tech: ['stackoverflow.com', 'github.com', 'medium.com', 'dev.to'],
            reference: ['wikipedia.org', 'britannica.com', 'dictionary.com']
        };

        for (const [category, sites] of Object.entries(categories)) {
            if (sites.some(site => hostname.includes(site))) {
                return category;
            }
        }

        return 'general';
    }

    static analyzePageStructure() {
        return {
            hasHeaders: document.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0,
            hasParagraphs: document.querySelectorAll('p').length > 0,
            hasLists: document.querySelectorAll('ul, ol').length > 0,
            hasCode: document.querySelectorAll('code, pre').length > 0,
            hasQuotes: document.querySelectorAll('blockquote').length > 0
        };
    }

    static processArticle(text, context) {
        const processed = SmartTextProcessor.preprocess(text, 'google');
        // Articles benefit from natural pacing
        processed.optimized = processed.optimized.replace(/\. /g, '. \\n');
        return processed;
    }

    static processDocumentation(text, context) {
        const processed = SmartTextProcessor.preprocess(text, 'google');
        // Documentation often has technical terms
        processed.optimized = processed.optimized
            .replace(/(\b[A-Z][a-z]+[A-Z][a-zA-Z]*)/g, (match) => {
                // Add spaces in camelCase for better pronunciation
                return match.replace(/([a-z])([A-Z])/g, '$1 $2');
            });
        return processed;
    }

    static processCode(text, context) {
        const processed = SmartTextProcessor.preprocess(text, 'google');
        // Code needs special handling for operators and syntax
        processed.optimized = processed.optimized
            .replace(/==/g, ' equals equals ')
            .replace(/!=/g, ' not equals ')
            .replace(/=>/g, ' arrow function ')
            .replace(/\${([^}]+)}/g, ' template literal $1 ')
            .replace(/\bfunction\b/g, 'function declaration')
            .replace(/\bconst\b/g, 'constant')
            .replace(/\blet\b/g, 'let variable')
            .replace(/\bvar\b/g, 'var variable');
        return processed;
    }

    static processEmail(text, context) {
        const processed = SmartTextProcessor.preprocess(text, 'elevenlabs'); // ElevenLabs is good for natural speech
        // Emails are usually conversational
        return processed;
    }
}

export { SmartTextProcessor, ContextualProcessor };