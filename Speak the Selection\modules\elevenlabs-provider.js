/**
 * ElevenLabs API Provider Module
 * Handles all ElevenLabs-specific functionality including voice fetching, filtering, and credits
 */

import { StorageManager } from './storage-manager.js';

export class ElevenLabsProvider {
    constructor(domElements) {
        this.dom = domElements;
        this.allVoices = [];
    }

    /**
     * Initialize ElevenLabs provider with event listeners
     */
    initialize() {
        // API key auto-save handler
        this.dom.apiKeyInput.addEventListener('input',
            StorageManager.createAutoSaveHandler(
                this.dom.apiKeyInput,
                'elevenLabsApiKey',
                (apiKey) => {
                    this.fetchAndDisplayCredits(apiKey);
                    this.fetchAndStoreVoices();
                    this.updateTtaInfo();
                }
            )
        );

        // Voice selection handler
        this.dom.voiceSelect.addEventListener('change', () => {
            StorageManager.saveSync({ selectedVoiceId: this.dom.voiceSelect.value });
            this.updateTtaInfo();
        });

        // Filter handlers
        this.dom.freeFilterToggle.addEventListener('click', () => {
            const isActive = !this.getToggleState(this.dom.freeFilterToggle);
            this.setToggleState(this.dom.freeFilterToggle, isActive);
            StorageManager.saveSync({ filterFree: isActive }).then(() => {
                this.applyFiltersAndPopulateVoices();
            });
        });

        this.dom.languageFilterSelect.addEventListener('change', () => {
            StorageManager.saveSync({ filterLang: this.dom.languageFilterSelect.value }).then(() => {
                this.applyFiltersAndPopulateVoices();
            });
        });

        this.dom.genderFilterSelect.addEventListener('change', () => {
            StorageManager.saveSync({ filterGender: this.dom.genderFilterSelect.value }).then(() => {
                this.applyFiltersAndPopulateVoices();
            });
        });

        // Slider handlers
        this.dom.speedSlider.addEventListener('input', () => {
            let speed = parseFloat(this.dom.speedSlider.value);
            speed = Math.max(0.7, Math.min(1.2, speed));
            this.dom.speedValue.textContent = speed.toFixed(2);
            StorageManager.saveSync({ playbackSpeed: speed });
        });

        this.dom.stabilitySlider.addEventListener('input', () => {
            const stability = parseFloat(this.dom.stabilitySlider.value);
            this.dom.stabilityValue.textContent = Math.round(stability * 100);
            StorageManager.saveSync({ stability: stability });
        });

        this.dom.similaritySlider.addEventListener('input', () => {
            const similarityBoost = parseFloat(this.dom.similaritySlider.value);
            this.dom.similarityValue.textContent = Math.round(similarityBoost * 100);
            StorageManager.saveSync({ similarityBoost: similarityBoost });
        });

        this.dom.styleSlider.addEventListener('input', () => {
            const style = parseFloat(this.dom.styleSlider.value);
            this.dom.styleValue.textContent = Math.round(style * 100);
            StorageManager.saveSync({ style: style });
        });
    }

    /**
     * Helper functions for toggle buttons
     */
    setToggleState(toggleElement, isActive) {
        if (isActive) {
            toggleElement.classList.add('active');
        } else {
            toggleElement.classList.remove('active');
        }
    }

    getToggleState(toggleElement) {
        return toggleElement.classList.contains('active');
    }

    /**
     * Fetch and display ElevenLabs credits
     * @param {string} apiKey - ElevenLabs API key
     */
    async fetchAndDisplayCredits(apiKey) {
        if (!apiKey) {
            this.dom.elevenLabsCreditsDiv.textContent = '';
            return;
        }

        this.dom.elevenLabsCreditsDiv.textContent = 'Loading credits...';

        try {
            const response = await fetch('https://api.elevenlabs.io/v1/user/subscription', {
                headers: { 'xi-api-key': apiKey }
            });

            if (!response.ok) throw new Error('Failed to fetch credits.');

            const data = await response.json();
            const remaining = data.character_limit - data.character_count;
            this.dom.elevenLabsCreditsDiv.textContent = `Remaining Credits: ${remaining.toLocaleString()}`;
            this.updateTtaInfo();
        } catch (error) {
            this.dom.elevenLabsCreditsDiv.textContent = 'Could not load credits.';
        }
    }

    /**
     * Fetch and store voices from ElevenLabs API
     */
    async fetchAndStoreVoices() {
        this.dom.elevenLabsVoiceStatus.textContent = 'Loading...';
        const apiKey = this.dom.apiKeyInput.value.trim();

        if (!apiKey) {
            this.updateStatus('Please enter an API key first.', 'red');
            return;
        }

        let nextPageToken = null;
        let fetchedVoices = [];

        try {
            do {
                const url = new URL('https://api.elevenlabs.io/v2/voices');
                url.searchParams.set('page_size', '100');
                if (nextPageToken) url.searchParams.set('next_page_token', nextPageToken);

                const response = await fetch(url.toString(), {
                    headers: { 'xi-api-key': apiKey }
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API Error: ${errorData?.detail?.message || response.statusText}`);
                }

                const data = await response.json();
                fetchedVoices.push(...(data.voices || []));
                nextPageToken = data.next_page_token || null;
            } while (nextPageToken);

            this.allVoices = fetchedVoices;

            await StorageManager.saveLocal({ allVoices: this.allVoices });

            this.populateLanguageFilter(this.allVoices);
            this.populateGenderFilter(this.allVoices);
            this.applyFiltersAndPopulateVoices();
            this.dom.elevenLabsVoiceStatus.textContent = `Loaded ${this.allVoices.length} voices!`;
        } catch (error) {
            this.dom.elevenLabsVoiceStatus.textContent = error.message;
        }
    }

    /**
     * Voice filtering and sorting utilities
     */
    isFreeAccessible(voice) {
        const freeFlag = voice?.sharing?.free_users_allowed === true;
        const inFreeTier = Array.isArray(voice?.available_for_tiers) &&
            voice.available_for_tiers.some(t => (t || "").toLowerCase() === "free");
        return freeFlag || inFreeTier;
    }

    getBestLangKey(voice) {
        const verified = Array.isArray(voice?.verified_languages) ? voice.verified_languages : [];
        if (verified.length > 0 && (verified[0].locale || verified[0].language)) {
            return (verified[0].locale || verified[0].language).toLowerCase();
        }
        if (voice?.labels?.accent) return voice.labels.accent.toLowerCase();
        return "zzz";
    }

    compositeSort(a, b) {
        const aIsFree = this.isFreeAccessible(a);
        const bIsFree = this.isFreeAccessible(b);
        if (aIsFree !== bIsFree) return aIsFree ? -1 : 1;

        const aLang = this.getBestLangKey(a);
        const bLang = this.getBestLangKey(b);
        if (aLang !== bLang) return aLang.localeCompare(bLang);

        return (a.name || "").toLowerCase().localeCompare((b.name || "").toLowerCase());
    }

    /**
     * Apply filters and populate voice dropdown
     */
    async applyFiltersAndPopulateVoices(initialSelectedVoiceId) {
        const { sync: syncData } = await StorageManager.loadAll();
        let selectedVoiceId = initialSelectedVoiceId || syncData.selectedVoiceId;
        let filteredVoices = [...this.allVoices];

        if (syncData.filterFree) {
            filteredVoices = filteredVoices.filter(voice => this.isFreeAccessible(voice));
        }

        this.populateLanguageFilter(filteredVoices, syncData.filterLang);
        this.populateGenderFilter(filteredVoices, syncData.filterGender);

        if (syncData.filterLang && syncData.filterLang !== 'all') {
            filteredVoices = filteredVoices.filter(v =>
                this.getBestLangKey(v).startsWith(syncData.filterLang)
            );
        }

        if (syncData.filterGender && syncData.filterGender !== 'all') {
            filteredVoices = filteredVoices.filter(v => v?.labels?.gender === syncData.filterGender);
        }

        const sortedVoices = filteredVoices.sort((a, b) => this.compositeSort(a, b));
        const isSelectedVoiceInList = sortedVoices.some(v => v.voice_id === selectedVoiceId);

        if (!isSelectedVoiceInList && sortedVoices.length > 0) {
            selectedVoiceId = sortedVoices[0].voice_id;
            await StorageManager.saveSync({ selectedVoiceId: selectedVoiceId });
        }

        this.populateVoiceSelect(sortedVoices, selectedVoiceId);
    }

    /**
     * Populate language filter dropdown
     */
    populateLanguageFilter(voices, selectedLang) {
        const languages = new Set();
        let hasMultilingual = false;

        voices.forEach(v => {
            const langKey = this.getBestLangKey(v);
            if (langKey === 'zzz') hasMultilingual = true;
            else languages.add(langKey);
        });

        const sortedLanguages = [...languages].sort();
        this.dom.languageFilterSelect.innerHTML = '<option value="all">All Languages</option>';

        if (hasMultilingual) {
            const option = document.createElement('option');
            option.value = 'zzz';
            option.textContent = 'multilingual';
            if ('zzz' === selectedLang) option.selected = true;
            this.dom.languageFilterSelect.appendChild(option);
        }

        sortedLanguages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang;
            option.textContent = lang;
            if (lang === selectedLang) option.selected = true;
            this.dom.languageFilterSelect.appendChild(option);
        });
    }

    /**
     * Populate gender filter dropdown
     */
    populateGenderFilter(voices, selectedGender) {
        const genders = [...new Set(voices.map(v => v?.labels?.gender).filter(Boolean))];
        this.dom.genderFilterSelect.innerHTML = '<option value="all">All Genders</option>';

        genders.sort().forEach(gender => {
            const option = document.createElement('option');
            option.value = gender;
            option.textContent = gender.charAt(0).toUpperCase() + gender.slice(1);
            if (gender === selectedGender) option.selected = true;
            this.dom.genderFilterSelect.appendChild(option);
        });
    }

    /**
     * Populate main voice selection dropdown
     */
    populateVoiceSelect(voices, selectedVoiceId) {
        this.dom.voiceSelect.innerHTML = '';

        if (!voices || voices.length === 0) {
            this.dom.voiceSelect.innerHTML = '<option>No voices match filters</option>';
            return;
        }

        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.voice_id;
            const { accent, description, age, use_case } = voice.labels || {};
            const details = [accent, description, age, use_case].filter(Boolean).join(', ');
            option.textContent = `${voice.name} (${details})`;
            if (voice.voice_id === selectedVoiceId) option.selected = true;
            this.dom.voiceSelect.appendChild(option);
        });
    }

    /**
     * Update Text-to-Audio info display
     */
    updateTtaInfo() {
        this.dom.ttaCreditsSpan.textContent = this.dom.elevenLabsCreditsDiv.textContent || 'Credits: Not available';
        this.dom.ttaCreditsSpan.style.cssText = 'color: var(--muted);';
        const selectedVoice = this.dom.voiceSelect.options[this.dom.voiceSelect.selectedIndex]?.text;
        this.dom.ttaVoiceSpan.textContent = `Voice: ${selectedVoice || 'N/A'}`;
    }

    /**
     * Restore settings from storage
     */
    async restoreSettings(syncData, localData) {
        // Restore API key
        if (syncData.elevenLabsApiKey) {
            this.dom.apiKeyInput.value = syncData.elevenLabsApiKey;
        }

        // Restore toggle states
        this.setToggleState(this.dom.freeFilterToggle, syncData.filterFree === true);

        // Restore slider values
        let initialSpeed = syncData.playbackSpeed || 1.0;
        initialSpeed = Math.max(0.7, Math.min(1.2, initialSpeed));
        this.dom.speedSlider.value = initialSpeed;
        this.dom.speedValue.textContent = parseFloat(initialSpeed).toFixed(2);

        const initialStability = syncData.stability || 0.5;
        this.dom.stabilitySlider.value = initialStability;
        this.dom.stabilityValue.textContent = Math.round(initialStability * 100);

        const initialSimilarity = syncData.similarityBoost || 0.75;
        this.dom.similaritySlider.value = initialSimilarity;
        this.dom.similarityValue.textContent = Math.round(initialSimilarity * 100);

        const initialStyle = syncData.style || 0;
        this.dom.styleSlider.value = initialStyle;
        this.dom.styleValue.textContent = Math.round(initialStyle * 100);

        // Populate voice lists from cache
        if (localData.allVoices) {
            this.allVoices = localData.allVoices;
            this.populateLanguageFilter(this.allVoices, syncData.filterLang);
            this.populateGenderFilter(this.allVoices, syncData.filterGender);
            this.applyFiltersAndPopulateVoices(syncData.selectedVoiceId);
        }

        // Fetch fresh credit info
        if (syncData.elevenLabsApiKey) {
            this.fetchAndDisplayCredits(syncData.elevenLabsApiKey);
        }
    }

    /**
     * Show provider-specific UI elements
     */
    show() {
        this.dom.elevenLabsSettings.style.display = 'block';
        this.dom.elevenLabsVoiceSettings.style.display = 'block';
    }

    /**
     * Hide provider-specific UI elements
     */
    hide() {
        this.dom.elevenLabsSettings.style.display = 'none';
        this.dom.elevenLabsVoiceSettings.style.display = 'none';
    }

    // Utility methods that need to be accessible from the provider
    updateStatus(message, color) {
        // This will be provided by the main popup controller
        console.log(`${color}: ${message}`);
    }
}