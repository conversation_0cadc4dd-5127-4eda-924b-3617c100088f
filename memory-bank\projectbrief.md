# Project Brief: TTS Chrome Extension

This project is a Chrome extension that provides Text-to-Speech (TTS) functionality. Users can select text on any webpage, and the extension will read it aloud using multiple TTS providers.

## Core Features:
- **Multi-provider Support:** The extension supports ElevenLabs, Hume AI, and Straico TTS providers.
- **Text Selection:** Users can select text on a webpage to activate the TTS functionality.
- **Context Menu Integration:** A "Read aloud" option is available in the context menu when text is selected, which also displays the estimated reading time, word count, and character count.
- **Floating Play Button:** A floating "Play" button appears near the selected text for easy access. It includes controls for play/pause, volume, playback speed, and a close button. The button is responsive to window resizing and selection direction.
- **Configurable Settings:** Users can configure the extension through a popup, including:
    - TTS provider selection
    - API keys for each provider
    - Voice selection with filtering
    - Playback speed and volume
    - Toggles for the floating play button and word highlighting
- **Background Audio Playback:** Audio is played in the background using an offscreen document to ensure uninterrupted playback.
- **Text Highlighting:** The extension highlights words as they are spoken for all providers. For providers that don't support it natively, a custom syllable-based timing estimation is used.
- **Long Text Handling:** The extension automatically splits long texts into smaller chunks for seamless playback.
- **Credit Usage Warning:** A confirmation dialog is shown for very long text selections to warn the user about high credit usage.
