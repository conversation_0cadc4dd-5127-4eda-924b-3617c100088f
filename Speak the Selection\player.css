/* =========================
   TTS Player — Modernized UI
   - Keeps your class names
   - Better contrast + accessibility
   - Touch-friendly sizes
   - Reduced-motion safe
========================= */

/* --- Theme Tokens --- */
:root {
    --primary-600: #1d4ed8;
    --primary-500: #3b82f6;
    --primary-400: #60a5fa;
    --primary: #2563eb;
    --bg: rgba(18, 23, 37, 0.75);
    --surface: rgba(22, 28, 44, 0.7);
    --stroke: rgba(255, 255, 255, 0.1);
    --text: #e5e7eb;
    --muted: #9ca3af;
    --chip: rgba(255, 255, 255, 0.06);
    --shadow: 0 12px 32px rgba(2, 6, 23, 0.45);
    --ring: 0 0 0 3px rgba(37, 99, 235, 0.35);
    --progress-track: rgba(255, 255, 255, 0.16);
    --progress-fill: #60a5fa;
    --radius-pill: 999px;
    --radius-lg: 16px;

    /* Optional: set via JS for dynamic fill on sliders (0%–100%) */
    --progress: 0;
    --seek: 0%;
    --volume: 60%;
    --speed: 33.33%;
}

/* --- Base Container --- */
#tts-player-container {
    position: fixed !important;
    z-index: 999999 !important;
    font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif !important;
    user-select: none !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    color: var(--text) !important;
    background: rgba(22, 28, 44, 0.95) !important;
    backdrop-filter: blur(12px) saturate(1.1) !important;
    border: 1px solid var(--stroke) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: 0 12px 32px rgba(2, 6, 23, 0.45);
    transition: transform .2s ease, box-shadow .2s ease, opacity .2s ease, width .2s ease, border-radius .2s ease, background .2s ease, border-color .2s ease;
    padding: 12px;
    min-width: 120px;
    min-height: 48px;
}

/* Initial minimized state */
#tts-player-container.minimized {
    border-radius: 999px !important;
    opacity: 0.4 !important;
    transition: opacity .2s ease;
    padding: 4px;
    min-width: auto !important;
    width: auto !important;
}

#tts-player-container.minimized:hover {
    opacity: 1 !important;
}

/* Improve visibility on dark backgrounds */
#tts-player-container.minimized[data-theme="dark"] {
    opacity: 0.8;
    border: 1px solid transparent;
    box-shadow: 0 0 15px 2px rgba(255, 255, 255, 0.25);
}

/* Loading state with white glow */
#tts-player-container.loading[data-theme="dark"],
#tts-player-container.loading {
    opacity: 1 !important;
    box-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(255, 255, 255, 0.6),
        0 0 30px rgba(255, 255, 255, 0.4) !important;
    border: 1px solid var(--stroke) !important;
}

/* Light theme loading state */
#tts-player-container.loading[data-theme="light"] {
    opacity: 1 !important;
    box-shadow:
        0 0 10px rgba(37, 99, 235, 0.8),
        0 0 20px rgba(37, 99, 235, 0.6),
        0 0 30px rgba(37, 99, 235, 0.4) !important;
    border: 1px solid rgba(37, 99, 235, 0.3) !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

#tts-player-container.loading {
    border-radius: 50% !important;
    width: 35px !important;
    height: 35px !important;
    padding: 0 !important;
    min-width: 35px !important;
    min-height: 35px !important;
}

/* Hover elevation */
#tts-player-container:hover {
    box-shadow: 0 14px 34px rgba(2, 6, 23, 0.55);
}

/* --- Player States --- */
.tts-minimized-player {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 4px;
    gap: 2px;
    background: transparent;
    width: 100%;
    height: 100%;
}

.tts-expanded-player,
.tts-loading-player {
    display: none !important;
    align-items: center;
    padding: 6px;
}

/* Show expanded/loading when class is present */
#tts-player-container.expanded .tts-expanded-player,
#tts-player-container.loading .tts-loading-player {
    display: flex !important;
}

/* Hide minimized player when another state is active */
#tts-player-container.expanded .tts-minimized-player,
#tts-player-container.loading .tts-minimized-player {
    display: none;
}

#tts-player-container.loading .tts-expanded-player {
    display: none;
}

/* Ensure loading/expanded players are hidden when not active */
#tts-player-container.minimized .tts-loading-player,
#tts-player-container.expanded .tts-loading-player,
#tts-player-container.minimized .tts-loader,
#tts-player-container.expanded .tts-loader {
    display: none !important;
}

/* --- Minimized Player Specific --- */
#tts-player-container.minimized .tts-play-pause-button {
    width: 36px;
    height: 36px;
}

/* Circular pulse on hover */
#tts-player-container.minimized .tts-play-pause-button:hover::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    box-shadow: 0 0 0 6px rgba(96, 165, 250, 0.15);
    pointer-events: none;
}

/* --- Expanded Player --- */
#tts-player-container.expanded {
    border-radius: 24px !important;
    width: clamp(300px, 35vw, 320px);
    min-height: 80px;
}

.tts-expanded-player {
    display: flex;
    width: 100%;
    padding: 8px;
    align-items: center;
    position: relative;
    gap: 8px;
    flex-wrap: nowrap;
}

.tts-expanded-left-panel {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 4px;
    margin-left: 0;
    min-width: 0;
}

.tts-expanded-top-row {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

/* Remove negative margin since we now have proper spacing */
.tts-expanded-player>.tts-drag-handle {
    margin-right: 0;
}

.tts-provider-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

/* Expanded close button positioning - moved to center-right for easier clicking */
.tts-expanded-player .tts-close-button {
    position: absolute !important;
    top: 50% !important;
    right: 8px !important;
    left: auto !important;
    transform: translateY(-50%) !important;
    z-index: 100 !important;
    flex-shrink: 0;
    order: unset !important;
    margin: 0 !important;
}

/* --- Loading Player --- */
.tts-loading-player {
    width: 100% !important;
    height: 100% !important;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 !important;
}

.tts-loader {
    width: 12px !important;
    height: 12px !important;
    border: 1.5px solid rgba(255, 255, 255, 0.2) !important;
    border-top-color: #60a5fa !important;
    border-radius: 50% !important;
    animation: spin 0.8s linear infinite !important;
}

/* --- Controls & Buttons --- */
.tts-drag-handle {
    width: 16px;
    height: 24px;
    display: grid;
    place-items: center;
    cursor: grab;
    opacity: 1;
    margin-right: 4px;
    padding: 2px;
    box-sizing: border-box;
    flex-shrink: 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.tts-drag-handle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tts-drag-handle:active {
    cursor: grabbing;
    background-color: rgba(255, 255, 255, 0.2);
}

.tts-drag-handle::before {
    content: '';
    width: 12px;
    height: 16px;
    background:
        radial-gradient(circle at 2px 2px, currentColor 1.5px, transparent 1.6px) 0 0/4px 4px,
        radial-gradient(circle at 2px 2px, currentColor 1.5px, transparent 1.6px) 2px 2px/4px 4px;
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.2s ease;
    opacity: 1;
}

.tts-drag-handle:hover::before {
    color: #ffffff;
}

/* Light theme drag handle */
[data-theme="light"] .tts-drag-handle::before {
    color: rgba(15, 23, 42, 0.8) !important;
}

[data-theme="light"] .tts-drag-handle:hover::before {
    color: #0f172a !important;
}

[data-theme="light"] .tts-drag-handle:hover {
    background-color: rgba(15, 23, 42, 0.1) !important;
}

.tts-play-pause-button {
    position: relative;
    width: 48px;
    height: 48px;
    display: grid;
    place-items: center;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    outline: none;
    color: #fff;
    background: linear-gradient(180deg, #60a5fa, #1d4ed8) !important;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1), 0 6px 12px rgba(37, 99, 235, 0.25);
    transition: transform .12s ease, box-shadow .12s ease, filter .2s ease;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
}

.tts-play-pause-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.12), 0 10px 18px rgba(37, 99, 235, 0.28);
}

.tts-play-pause-button:active {
    transform: translateY(0);
    filter: brightness(.98);
}

.tts-play-pause-button:focus-visible {
    box-shadow: var(--ring), 0 6px 12px rgba(37, 99, 235, 0.25);
}

/* Progress Ring (for minimized state) */
#tts-player-container.loading .tts-play-pause-button::before {
    display: none;
}

#tts-player-container.minimized .tts-play-pause-button::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: conic-gradient(var(--progress-fill) calc(var(--progress, 0) * 1turn), transparent 0);
    -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 4px), #000 0);
    mask: radial-gradient(farthest-side, transparent calc(100% - 4px), #000 0);
    transition: background 0.12s linear;
}

/* Ring base track */
#tts-player-container.minimized .tts-play-pause-button::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: conic-gradient(var(--progress-track) 1turn, var(--progress-track) 0);
    -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 4px), #000 0);
    mask: radial-gradient(farthest-side, transparent calc(100% - 4px), #000 0);
    opacity: .6;
}

/* --- Labels and Values --- */
.tts-provider-label,
.tts-time-label {
    color: #ffffff !important;
    opacity: 1 !important;
    font-size: 13px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Light theme labels */
[data-theme="light"] .tts-provider-label,
[data-theme="light"] .tts-time-label {
    color: #0f172a !important;
    font-weight: 600 !important;
    text-shadow: none !important;
}

.tts-volume-value,
.tts-speed-value {
    color: #ffffff !important;
    opacity: 1 !important;
    font-size: 12px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Light theme values */
[data-theme="light"] .tts-volume-value,
[data-theme="light"] .tts-speed-value {
    color: #0f172a !important;
    font-weight: 600 !important;
    text-shadow: none !important;
}

/* --- Expanded Controls --- */
.tts-progress-container {
    grid-area: progress;
    display: grid;
    align-items: center;
    gap: 4px;
    grid-template-columns: 1fr auto;
    position: relative;
}

.tts-progress-slider {
    width: 100%;
    height: 8px;
    appearance: none;
    -webkit-appearance: none;
    background: linear-gradient(90deg,
            var(--progress-fill, #60a5fa) var(--seek, 0%),
            var(--progress-track, rgba(255, 255, 255, 0.16)) var(--seek, 0%));
    border-radius: 999px;
    outline: none;
    cursor: pointer;
}

.tts-progress-slider:hover {
    filter: brightness(1.05);
}

.tts-progress-slider:focus-visible {
    box-shadow: var(--ring);
}

.tts-progress-slider::-webkit-slider-thumb {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ffffff !important;
    border: 2px solid var(--primary, #2563eb);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.tts-progress-slider::-moz-range-track {
    height: 8px;
    background:
        linear-gradient(90deg, var(--progress-fill) var(--seek, 0%), var(--progress-track) var(--seek, 0%));
    border-radius: 999px;
}

.tts-progress-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid var(--primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Right panel grouping */
.tts-right-panel {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    flex-grow: 1;
    visibility: visible !important;
    opacity: 1 !important;
    margin-right: 32px;
    /* Add space for close button */
}

/* Volume (Vertical) */
.tts-vertical-slider-container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: 6px;
    justify-items: center;
    font-size: 11px;
    color: #9ca3af !important;
}

.tts-vertical-slider {
    -webkit-appearance: none !important;
    appearance: none !important;
    writing-mode: vertical-lr;
    direction: rtl;
    width: 8px !important;
    height: 84px;
    border-radius: 999px;
    outline: none;
    cursor: pointer;
}

.tts-vertical-slider:hover {
    filter: brightness(1.05);
}

.tts-vertical-slider:focus-visible {
    box-shadow: var(--ring);
}

.tts-vertical-slider::-webkit-slider-thumb {
    -webkit-appearance: none !important;
    appearance: none !important;
    width: 16px !important;
    height: 16px !important;
    border-radius: 50%;
    background: #ffffff !important;
    border: 2px solid var(--primary, #2563eb) !important;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.tts-volume-slider {
    background: linear-gradient(to top,
            var(--progress-fill, #60a5fa) var(--volume, 60%),
            var(--progress-track, rgba(255, 255, 255, 0.16)) var(--volume, 60%)) !important;
}

.tts-speed-slider {
    background: linear-gradient(to top,
            var(--progress-fill, #60a5fa) var(--speed, 33.33%),
            var(--progress-track, rgba(255, 255, 255, 0.16)) var(--speed, 33.33%)) !important;
}

/* Light theme slider tracks - use dark gray instead of white */
[data-theme="light"] .tts-volume-slider {
    background: linear-gradient(to top,
            var(--progress-fill, #3b82f6) var(--volume, 60%),
            rgba(15, 23, 42, 0.3) var(--volume, 60%)) !important;
}

[data-theme="light"] .tts-speed-slider {
    background: linear-gradient(to top,
            var(--progress-fill, #3b82f6) var(--speed, 33.33%),
            rgba(15, 23, 42, 0.3) var(--speed, 33.33%)) !important;
}

/* Light theme progress slider track */
[data-theme="light"] .tts-progress-slider {
    background: linear-gradient(90deg,
            var(--progress-fill, #3b82f6) var(--seek, 0%),
            rgba(15, 23, 42, 0.3) var(--seek, 0%)) !important;
}

/* --- Word Highlighting --- */
.tts-highlight {
    background: #ffec8a;
    color: #111;
    padding: 0 .18em;
    border-radius: 4px;
    box-shadow: 0 0 0 1px rgba(255, 220, 80, 0.6) inset;
    transition: background .15s ease, box-shadow .15s ease;
}

@media (prefers-reduced-motion: reduce) {
    .tts-highlight {
        transition: none !important;
    }
}

/* --- Positioning presets --- */
#tts-player-container.fixed {
    position: fixed;
}

#tts-player-container.pos-br {
    right: 16px;
    bottom: 16px;
}

#tts-player-container.pos-tr {
    right: 16px;
    top: 16px;
}

#tts-player-container.pos-bl {
    left: 16px;
    bottom: 16px;
}

#tts-player-container.pos-tl {
    left: 16px;
    top: 16px;
}

/* --- Animations --- */
@keyframes spin {
    from {
        transform: rotate(0);
    }

    to {
        transform: rotate(360deg);
    }
}

@media (prefers-reduced-motion: reduce) {

    #tts-player-container,
    .tts-play-pause-button,
    .tts-loader {
        transition: none !important;
        animation: none !important;
    }
}

@media (forced-colors: active) {
    #tts-player-container {
        border: 1px solid CanvasText;
    }

    .tts-play-pause-button,
    .tts-close-button,
    .tts-progress-slider,
    input[type="range"][orient="vertical"] {
        forced-color-adjust: none;
        border: 1px solid CanvasText;
    }
}

/* --- Responsive tweaks --- */
@media (max-width: 480px) {
    #tts-player-container.expanded {
        width: calc(100vw - 24px);
        border-radius: 14px;
    }

    .tts-provider-label {
        font-size: 12px;
    }

    .tts-play-pause-button {
        width: 44px;
        height: 44px;
    }

    .tts-progress-slider {
        height: 7px;
    }
}

/* --- Light theme support --- */
[data-theme="light"] #tts-player-container {
    --text: #0f172a !important;
    --muted: #475569 !important;
    --surface: rgba(255, 255, 255, 0.95) !important;
    --stroke: rgba(2, 6, 23, 0.15) !important;
    --progress-track: rgba(2, 6, 23, 0.2) !important;
    --chip: rgba(2, 6, 23, 0.06) !important;
    color: var(--text) !important;
    background: var(--surface) !important;
    backdrop-filter: blur(12px) !important;
    border: 1px solid var(--stroke) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Force light theme text visibility */
[data-theme="light"] .tts-provider-label,
[data-theme="light"] .tts-time-label,
[data-theme="light"] .tts-volume-value,
[data-theme="light"] .tts-speed-value,
[data-theme="light"] .tts-vertical-slider-container label {
    color: #0f172a !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Dark theme enhancements */
[data-theme="dark"] #tts-player-container {
    --text: #e5e7eb !important;
    --muted: #94a3b8 !important;
    --surface: rgba(22, 28, 44, 0.95) !important;
    --stroke: rgba(255, 255, 255, 0.1) !important;
    --progress-track: rgba(255, 255, 255, 0.16) !important;
    background: var(--surface) !important;
    border: 1px solid var(--stroke) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Force dark theme text visibility */
[data-theme="dark"] .tts-provider-label,
[data-theme="dark"] .tts-time-label,
[data-theme="dark"] .tts-volume-value,
[data-theme="dark"] .tts-speed-value,
[data-theme="dark"] .tts-vertical-slider-container label {
    color: #e5e7eb !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

@media (prefers-color-scheme: dark) {
    #tts-player-container:not([data-theme="light"]) {
        background: rgba(22, 28, 44, 0.96) !important;
    }
}

/* Shadow host styling */
#tts-player-shadow-host {
    border-radius: 999px !important;
    transition: border-radius 0.2s ease, width 0.2s ease, height 0.2s ease;
}

.loading-active #tts-player-shadow-host {
    border-radius: 50% !important;
    width: 56px !important;
    height: 56px !important;
}

/* Vertical slider labels */
.tts-vertical-slider-container label {
    color: #ffffff !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    opacity: 1 !important;
    margin-top: 6px;
    display: block !important;
    visibility: visible !important;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Light theme labels */
[data-theme="light"] .tts-vertical-slider-container label {
    color: #0f172a !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(15, 23, 42, 0.1) !important;
    text-shadow: none !important;
}

/* Volume and speed values */
.tts-vertical-slider-container .tts-volume-value,
.tts-vertical-slider-container .tts-speed-value {
    font-size: 11px !important;
    font-weight: 600 !important;
    color: #ffffff !important;
    background-color: rgba(0, 0, 0, 0.6) !important;
    padding: 3px 8px;
    border-radius: 8px;
    margin-bottom: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    min-width: 32px;
    text-align: center;
    opacity: 1 !important;
    visibility: visible !important;
    line-height: 1.2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Light theme values */
[data-theme="light"] .tts-vertical-slider-container .tts-volume-value,
[data-theme="light"] .tts-vertical-slider-container .tts-speed-value {
    color: #0f172a !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(15, 23, 42, 0.2) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-shadow: none !important;
}

/* Reset speed button */
.tts-reset-speed-button {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #e5e7eb !important;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 16px !important;
    cursor: pointer;
    transition: background .15s ease, transform .15s ease;
    display: grid;
    place-items: center;
    opacity: 1 !important;
    visibility: visible !important;
}

.tts-reset-speed-button:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: rotate(-45deg);
}

.tts-reset-speed-button:active {
    transform: rotate(-90deg) scale(0.95);
}

.tts-provider-container .tts-reset-speed-button {
    background: linear-gradient(135deg, #60a5fa, #3b82f6) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tts-provider-container .tts-reset-speed-button:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Close button */
.tts-close-button {
    all: unset !important;
    width: 24px !important;
    height: 24px !important;
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
    border-radius: 50% !important;
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: bold !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    line-height: 1 !important;
    flex-shrink: 0 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.tts-close-button:hover {
    background: rgba(255, 67, 54, 0.8) !important;
    border-color: rgba(255, 67, 54, 1) !important;
    color: #ffffff !important;
    box-shadow: 0 3px 6px rgba(255, 67, 54, 0.4) !important;
}

/* Specific hover for expanded close button to maintain centering */
.tts-expanded-player .tts-close-button:hover {
    transform: translateY(-50%) scale(1.05) !important;
}

.tts-close-button:active {
    transform: scale(0.95) !important;
}

/* Specific active for expanded close button to maintain centering */
.tts-expanded-player .tts-close-button:active {
    transform: translateY(-50%) scale(0.95) !important;
}

/* Light theme close button */
[data-theme="light"] .tts-close-button {
    background: rgba(15, 23, 42, 0.1) !important;
    border-color: rgba(15, 23, 42, 0.3) !important;
    color: #0f172a !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .tts-close-button:hover {
    background: rgba(220, 38, 38, 0.8) !important;
    border-color: rgba(220, 38, 38, 1) !important;
    color: #ffffff !important;
    box-shadow: 0 3px 6px rgba(220, 38, 38, 0.3) !important;
}

/* Light theme expanded close button hover with centering */
[data-theme="light"] .tts-expanded-player .tts-close-button:hover {
    transform: translateY(-50%) scale(1.05) !important;
}

[data-theme="light"] .tts-expanded-player .tts-close-button:active {
    transform: translateY(-50%) scale(0.95) !important;
}

/* Minimized close button positioning */
.tts-minimized-player .tts-close-button {
    order: 999;
    margin-left: auto;
    margin-right: 6px;
    display: flex !important;
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
    min-width: 20px !important;
    max-width: 20px !important;
    overflow: hidden !important;
}

/* Force visibility */
.tts-expanded-player button,
.tts-expanded-player label,
.tts-expanded-player span {
    opacity: 1 !important;
    visibility: visible !important;
}

.tts-expanded-player {
    color: #e5e7eb !important;
}

.tts-expanded-player * {
    color: inherit;
}

.tts-vertical-slider-container * {
    color: inherit !important;
}