/**
 * Audio State Manager Module
 * Handles audio playback state management, synchronization, and speed control
 */

import { PlaybackState } from '../shared/constants.js';

export class AudioStateManager {
    constructor() {
        this.currentState = PlaybackState.IDLE;
        this.userActivelyChangedSpeed = false;
        this.currentSessionSpeed = 1.0;

        // Callbacks
        this.onStateChange = null;
        this.onSpeedChange = null;
        this.onProgressUpdate = null;
    }

    /**
     * Initialize the audio state manager
     */
    initialize() {
        this.setupStateListeners();
        this.initializePlaybackState();
    }

    /**
     * Set up state change listeners
     */
    setupStateListeners() {
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'sync' && changes.showFloatingButton) {
                // Handle floating button changes
                if (this.onStateChange) {
                    this.onStateChange('showFloatingButton', changes.showFloatingButton.newValue);
                }
            }
        });
    }

    /**
     * Initialize playback state from background script
     */
    initializePlaybackState() {
        chrome.runtime.sendMessage({ type: 'get-playback-state' }, (response) => {
            if (response && response.playbackState) {
                this.currentState = PlaybackState.IDLE;
            }
        });
    }

    /**
     * Sync UI state with actual audio playback state
     */
    async syncPlaybackState() {
        if (!chrome.runtime || !chrome.runtime.id) return;

        try {
            // Request current audio state from background
            const response = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ type: 'get-current-audio-state' }, resolve);
            });

            if (response && response.state) {
                // Update UI state to match actual audio state
                const actualState = response.state;
                if (this.currentState !== actualState) {
                    console.log(`State sync: UI was ${this.currentState}, actual is ${actualState}`);
                    this.currentState = actualState;

                    // Notify UI components of state change
                    if (this.onStateChange) {
                        this.onStateChange('playback', actualState);
                    }
                }
            }

            // Sync speed values with storage
            await this.syncSpeedWithStorage();
        } catch (error) {
            console.warn('Failed to sync playback state:', error);
        }
    }

    /**
     * Enhanced speed synchronization function
     */
    async syncSpeedWithStorage() {
        try {
            // Get current provider and speed values from storage
            const result = await new Promise(resolve => {
                chrome.storage.sync.get(['selectedProvider', 'playbackSpeed', 'humePlaybackSpeed', 'straicoPlaybackSpeed'], resolve);
            });

            const provider = result.selectedProvider || 'google';
            let actualSpeed;

            // If user has actively changed speed in this session, use that
            if (this.userActivelyChangedSpeed) {
                actualSpeed = this.currentSessionSpeed;
                console.log(`Speed sync: Using user's session speed ${actualSpeed}x`);
            } else {
                // First load or no user changes - default to 1.0x
                actualSpeed = 1.0;
                console.log(`Speed sync: Using default 1.0x (no user changes)`);
            }

            // Notify speed change if callback is set
            if (this.onSpeedChange) {
                this.onSpeedChange(actualSpeed, provider);
            }

        } catch (error) {
            console.warn('Failed to sync speed with storage:', error);
        }
    }

    /**
     * Handle playback state changes from messages
     */
    handleStateChange(newState) {
        const oldState = this.currentState;
        this.currentState = newState;

        console.log(`State changed: ${oldState} -> ${newState}`);

        // Handle state-specific logic
        switch (newState) {
            case PlaybackState.PLAYING:
                // Sync speed when playback starts
                this.syncSpeedWithStorage().catch(() => {
                    console.warn('Failed to sync speed on playback start');
                });
                break;
            case PlaybackState.ENDED:
            case PlaybackState.STOPPED:
                this.currentState = PlaybackState.IDLE;
                break;
        }

        // Notify components of state change
        if (this.onStateChange) {
            this.onStateChange('playback', newState);
        }

        return oldState !== newState;
    }

    /**
     * Handle user speed changes
     */
    handleUserSpeedChange(speed) {
        // Mark as user action
        this.userActivelyChangedSpeed = true;
        this.currentSessionSpeed = speed;
        console.log(`User actively changed speed to ${speed}x`);

        // Save to storage based on provider
        chrome.storage.sync.get('selectedProvider', (result) => {
            const provider = result.selectedProvider;
            if (!provider) {
                console.warn('No TTS provider selected');
                return;
            }

            let speedSetting = {};
            if (provider === 'hume') speedSetting = { humePlaybackSpeed: speed };
            else if (provider === 'straico') speedSetting = { straicoPlaybackSpeed: speed };
            else speedSetting = { playbackSpeed: speed };

            chrome.storage.sync.set(speedSetting);
        });

        // Notify components of speed change
        if (this.onSpeedChange) {
            this.onSpeedChange(speed);
        }
    }

    /**
     * Reset speed to default for new selection
     */
    resetSpeedForNewSelection() {
        this.userActivelyChangedSpeed = false;
        this.currentSessionSpeed = 1.0;
        console.log('New text selection - reset speed to 1.0x default');
    }

    /**
     * Handle progress updates
     */
    handleProgressUpdate(currentTime, duration) {
        const fraction = duration > 0 ? currentTime / duration : 0;

        if (this.onProgressUpdate) {
            this.onProgressUpdate({
                currentTime,
                duration,
                fraction,
                formattedTime: this.formatTime(currentTime),
                formattedDuration: this.formatTime(duration)
            });
        }
    }

    /**
     * Format time for display
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Send playback control messages
     */
    sendPlaybackMessage(type, data = {}) {
        if (chrome.runtime && chrome.runtime.id) {
            chrome.runtime.sendMessage({ type, ...data }).catch(() => {
                console.warn(`Failed to send ${type} message`);
            });
        }
    }

    /**
     * Play/pause/resume based on current state
     */
    togglePlayback(text = null) {
        if (this.currentState === PlaybackState.LOADING) return;

        let action;
        if (this.currentState === PlaybackState.PLAYING) {
            action = 'pause';
        } else if (this.currentState === PlaybackState.PAUSED) {
            action = 'resume';
        } else {
            action = 'read-text';
        }

        if (action === 'read-text' && text) {
            if (text.length > 4000 && !confirm('The selected text is very long... proceed?')) return;
            this.sendPlaybackMessage('read-text', { text });
        } else {
            this.sendPlaybackMessage(action);
        }
    }

    /**
     * Seek to specific time
     */
    seekTo(time) {
        this.sendPlaybackMessage('seek-audio', { time });
    }

    /**
     * Set volume
     */
    setVolume(volume) {
        this.sendPlaybackMessage('set-volume', { volume });
    }

    /**
     * Set playback speed
     */
    setSpeed(speed) {
        this.handleUserSpeedChange(speed);
        this.sendPlaybackMessage('set-speed', { speed });
    }

    /**
     * Stop playback
     */
    stop() {
        this.sendPlaybackMessage('pause');
        this.currentState = PlaybackState.IDLE;
    }

    /**
     * Get current state
     */
    getCurrentState() {
        return this.currentState;
    }

    /**
     * Check if currently playing
     */
    isPlaying() {
        return this.currentState === PlaybackState.PLAYING;
    }

    /**
     * Check if paused
     */
    isPaused() {
        return this.currentState === PlaybackState.PAUSED;
    }

    /**
     * Check if idle
     */
    isIdle() {
        return this.currentState === PlaybackState.IDLE;
    }

    /**
     * Check if loading
     */
    isLoading() {
        return this.currentState === PlaybackState.LOADING;
    }

    /**
     * Get speed info
     */
    getSpeedInfo() {
        return {
            currentSessionSpeed: this.currentSessionSpeed,
            userActivelyChangedSpeed: this.userActivelyChangedSpeed
        };
    }

    /**
     * Set callback functions
     */
    setCallbacks(callbacks) {
        if (callbacks.onStateChange) {
            this.onStateChange = callbacks.onStateChange;
        }
        if (callbacks.onSpeedChange) {
            this.onSpeedChange = callbacks.onSpeedChange;
        }
        if (callbacks.onProgressUpdate) {
            this.onProgressUpdate = callbacks.onProgressUpdate;
        }
    }

    /**
     * Reset state for cleanup
     */
    reset() {
        this.currentState = PlaybackState.IDLE;
        this.userActivelyChangedSpeed = false;
        this.currentSessionSpeed = 1.0;
    }
}