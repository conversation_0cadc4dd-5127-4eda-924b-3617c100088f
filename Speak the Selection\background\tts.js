import { providers } from './providers.js';
import { splitIntoSentences, groupSentencesIntoChunks } from '../shared/utils.js';
import { playAudioQueue, stopAudio } from './audio.js';

// Badge management for progress indication
let progressTimer = null;

function updateBadge(text, color = '#4CAF50') {
    chrome.action.setBadgeText({ text: text });
    chrome.action.setBadgeBackgroundColor({ color: color });
}

function clearBadge() {
    chrome.action.setBadgeText({ text: '' });
    if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
    }
}

// Note: Historical timing removed - streaming providers are predictable, 
// only Google TTS needs smart text-based estimation

// Enhanced text-based time estimation for Google TTS
function estimateGoogleTTSTime(text, chunkCount) {
    // Base processing time calculations for Google TTS
    const baseTimePerChar = 0.08; // 80ms per character (conservative estimate)
    const chunkOverhead = 2.0; // 2 seconds overhead per chunk (API call setup)
    const complexityMultiplier = calculateTextComplexity(text);

    // Calculate base time
    const textProcessingTime = text.length * baseTimePerChar;
    const overheadTime = chunkCount * chunkOverhead;

    // Apply complexity and safety buffers
    const complexityAdjustedTime = textProcessingTime * complexityMultiplier;
    const totalTime = complexityAdjustedTime + overheadTime;

    // Add safety buffer for Google TTS variability
    const safetyBuffer = 1.4; // 40% safety buffer

    return Math.ceil(totalTime * safetyBuffer);
}

// Calculate text complexity factor for more accurate estimation
function calculateTextComplexity(text) {
    let complexityScore = 1.0; // Base complexity

    // Analyze text characteristics
    const words = text.split(/\s+/);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    // Word complexity factors
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    if (avgWordLength > 6) complexityScore += 0.1; // Longer words = more complex
    if (avgWordLength > 8) complexityScore += 0.1;

    // Sentence length factors
    const avgSentenceLength = text.length / sentences.length;
    if (avgSentenceLength > 100) complexityScore += 0.1; // Long sentences = more complex
    if (avgSentenceLength > 200) complexityScore += 0.2;

    // Special character complexity
    const specialChars = (text.match(/[^a-zA-Z0-9\s.,!?]/g) || []).length;
    const specialCharRatio = specialChars / text.length;
    if (specialCharRatio > 0.05) complexityScore += 0.1; // 5%+ special chars
    if (specialCharRatio > 0.10) complexityScore += 0.1; // 10%+ special chars

    // Numbers and technical content
    const numbersCount = (text.match(/\d+/g) || []).length;
    if (numbersCount > words.length * 0.1) complexityScore += 0.1; // Many numbers

    // Capitalization patterns (all caps sections are harder to process)
    const allCapsWords = (text.match(/\b[A-Z]{3,}\b/g) || []).length;
    if (allCapsWords > words.length * 0.05) complexityScore += 0.1;

    // Language complexity indicators
    const commasCount = (text.match(/,/g) || []).length;
    const complexPunctuation = (text.match(/[;:()\[\]{}"']/g) || []).length;
    if ((commasCount + complexPunctuation) > text.length * 0.03) complexityScore += 0.1;

    // Cap complexity to reasonable limits
    return Math.min(complexityScore, 2.0); // Max 2x complexity
}

async function estimateProcessingTime(textLength, chunkCount, providerName, actualText = '') {
    // For Google TTS, use smart text-based estimation
    if (providerName === 'google' && actualText) {
        return estimateGoogleTTSTime(actualText, chunkCount);
    }

    // For streaming providers (ElevenLabs, Hume, Straico), use simple predictable timing
    const streamingTimePerChunk = {
        'elevenlabs': 2.0,  // Streaming providers are fast and predictable
        'hume': 2.5,
        'straico': 2.0,
        'google': 8.0       // Fallback if no actual text provided
    };

    const timePerChunk = streamingTimePerChunk[providerName] || 2.0;
    return Math.ceil(chunkCount * timePerChunk);
}

function startProgressTimer(totalEstimatedTime, chunkCount, providerName) {
    let remainingTime = totalEstimatedTime;

    // Update badge immediately with provider-specific styling
    const color = providerName === 'google' ? '#FF9800' : '#2196F3'; // Orange for Google (slower)
    updateBadge(`${remainingTime}s`, color);

    progressTimer = setInterval(() => {
        remainingTime--;
        if (remainingTime > 0) {
            updateBadge(`${remainingTime}s`, color);
        } else {
            // Show waiting indicator if estimate was too low
            updateBadge('...', '#FF9800');
        }
    }, 1000);
}

// Enhanced progress tracking for Google TTS
function sendEnhancedProgress(tabId, currentChunk, totalChunks, startTime, providerName, status = '') {
    if (providerName !== 'google') return; // Only for Google TTS

    const elapsed = (Date.now() - startTime) / 1000;
    const progress = currentChunk / totalChunks;
    const avgTimePerChunk = currentChunk > 0 ? elapsed / currentChunk : 0;
    const estimatedRemaining = Math.ceil((totalChunks - currentChunk) * avgTimePerChunk);

    chrome.tabs.sendMessage(tabId, {
        type: 'enhanced-progress-update',
        data: {
            currentChunk,
            totalChunks,
            progress: Math.min(progress, 1),
            estimatedRemaining: Math.max(estimatedRemaining, 0),
            elapsed: Math.floor(elapsed),
            status: status || `Processing chunk ${currentChunk} of ${totalChunks}`,
            provider: providerName
        }
    }).catch(() => { });
}

// Heartbeat progress system for long single API calls
let heartbeatInterval = null;

function startHeartbeatProgress(tabId, chunkText, providerName, startTime) {
    if (providerName !== 'google') return;

    // Only start heartbeat for very long texts (>8000 chars)
    if (chunkText.length < 8000) return;

    // Clear any existing heartbeat
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }

    let heartbeatCount = 0;
    const textLength = chunkText.length;

    // Use our improved text-based estimation for heartbeat
    const estimatedTime = estimateGoogleTTSTime(chunkText, 1); // Single chunk estimation

    // Get text complexity info for display
    const complexity = calculateTextComplexity(chunkText);
    const complexityLevel = complexity > 1.5 ? 'complex' : complexity > 1.2 ? 'moderate' : 'simple';

    // Send initial heartbeat with enhanced information
    chrome.tabs.sendMessage(tabId, {
        type: 'heartbeat-progress-update',
        data: {
            textLength,
            estimatedTime: Math.ceil(estimatedTime),
            elapsed: 0,
            status: `Processing large ${complexityLevel} text (${textLength.toLocaleString()} characters)...`,
            provider: providerName,
            isHeartbeat: true,
            complexity: complexityLevel
        }
    }).catch(() => { });

    // Send heartbeat every 3 seconds
    heartbeatInterval = setInterval(() => {
        heartbeatCount++;
        const elapsed = (Date.now() - startTime) / 1000;
        const remainingEstimate = Math.max(5, estimatedTime - elapsed);

        // Create encouraging messages based on complexity and progress
        const baseMessages = [
            `Processing large ${complexityLevel} text (${textLength.toLocaleString()} characters)...`,
            'Google TTS is analyzing your text...',
            'Converting text to high-quality audio...',
            'Generating natural speech patterns...',
            'Almost ready, finalizing audio...'
        ];

        // Add complexity-specific messages
        if (complexityLevel === 'complex') {
            baseMessages.splice(2, 0, 'Processing complex text structure...');
            baseMessages.splice(3, 0, 'Handling technical content...');
        }

        const messageIndex = Math.min(Math.floor(heartbeatCount / 2), baseMessages.length - 1);

        chrome.tabs.sendMessage(tabId, {
            type: 'heartbeat-progress-update',
            data: {
                textLength,
                estimatedTime: Math.ceil(remainingEstimate),
                elapsed: Math.floor(elapsed),
                status: baseMessages[messageIndex],
                provider: providerName,
                isHeartbeat: true,
                heartbeatCount,
                complexity: complexityLevel
            }
        }).catch(() => { });

    }, 3000); // Every 3 seconds
}

function stopHeartbeatProgress() {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }
}

function updateProgressForChunk(currentChunk, totalChunks, startTime) {
    const elapsed = (Date.now() - startTime) / 1000;
    const avgTimePerChunk = elapsed / currentChunk;
    const remainingChunks = totalChunks - currentChunk;
    const estimatedRemaining = Math.ceil(remainingChunks * avgTimePerChunk);

    // Clear the timer and update with real-time calculation
    if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
    }

    if (estimatedRemaining > 0) {
        updateBadge(`${estimatedRemaining}s`, '#2196F3');
    } else {
        updateBadge('1s', '#4CAF50');
    }
}

// Validate provider-specific settings
function validateProviderSettings(providerName, settings) {
    switch (providerName) {
        case 'google':
            if (!settings.googleApiKey) {
                return 'Google API key is required. Please click the "🔑 Get API key" link in the extension popup to get your free Google API key.';
            }
            // Google TTS has a fallback to "Kore" voice, so voice selection is optional
            break;

        case 'elevenlabs':
            if (!settings.elevenLabsApiKey) {
                return 'ElevenLabs API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your ElevenLabs API key.';
            }
            if (!settings.selectedVoiceId) {
                return 'ElevenLabs voice is required. Please select a voice in the extension popup.';
            }
            break;

        case 'hume':
            if (!settings.humeApiKey) {
                return 'Hume API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your Hume API key.';
            }
            if (!settings.selectedHumeVoiceId || !settings.selectedHumeVoiceName) {
                return 'Hume voice is required. Please select a voice in the extension popup.';
            }
            break;

        case 'straico':
            if (!settings.straicoApiKey) {
                return 'Straico API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your Straico API key.';
            }
            if (!settings.selectedStraicoVoiceId) {
                return 'Straico voice is required. Please select a voice in the extension popup.';
            }
            break;

        default:
            return `Unknown provider: ${providerName}`;
    }
    return null; // No validation errors
}

export async function processTextForTTS(text, tabId, fromClipboard) {
    await stopAudio();

    try {
        // Send loading state to content script first
        chrome.tabs.sendMessage(tabId, { type: 'tts-loading' });

        await chrome.storage.session.set({ ttsTabId: tabId });

        const settings = await chrome.storage.sync.get(['selectedProvider', 'elevenLabsApiKey', 'humeApiKey', 'straicoApiKey', 'googleApiKey', 'selectedVoiceId', 'selectedHumeVoiceId', 'selectedHumeVoiceName', 'selectedStraicoVoiceId', 'selectedStraicoModel', 'humePlaybackSpeed', 'playbackSpeed', 'stability', 'similarityBoost', 'style', 'selectedGoogleVoice', 'enableHighlighting']);

        // Validate provider selection
        const providerName = settings.selectedProvider;
        if (!providerName) {
            throw new Error('No TTS provider selected. Please select a provider in the extension popup.');
        }

        const provider = providers[providerName];
        if (!provider) {
            throw new Error(`Provider "${providerName}" not found.`);
        }

        // Validate provider-specific requirements
        const validationError = validateProviderSettings(providerName, settings);
        if (validationError) {
            throw new Error(validationError);
        }

        // Note: Voice validation is now handled by validateProviderSettings() function above

        const chunkSize = providerName === 'google' ? 6000 : 3000;
        const sentences = splitIntoSentences(text);
        const textChunks = groupSentencesIntoChunks(sentences, chunkSize);

        // Initialize progress tracking
        const totalEstimatedTime = await estimateProcessingTime(text.length, textChunks.length, providerName, text);
        const startTime = Date.now();
        startProgressTimer(totalEstimatedTime, textChunks.length, providerName);

        // For Google TTS with multiple chunks, show enhanced progress immediately
        if (providerName === 'google' && textChunks.length > 2) {
            sendEnhancedProgress(tabId, 0, textChunks.length, startTime, providerName, 'Preparing to process text...');
        }

        const audioQueue = [];
        for (let i = 0; i < textChunks.length; i++) {
            const chunk = textChunks[i];
            const chunkStartTime = Date.now();

            // Update progress based on actual processing time
            if (i > 0) {
                updateProgressForChunk(i, textChunks.length, startTime);
            }

            // Show special indicator for Google TTS (slower provider)
            if (providerName === 'google') {
                updateBadge(`G${i + 1}/${textChunks.length}`, '#FF9800');
                // Send enhanced progress update
                sendEnhancedProgress(tabId, i + 1, textChunks.length, startTime, providerName, `Processing chunk ${i + 1} of ${textChunks.length}`);

                // Start heartbeat for very long chunks
                startHeartbeatProgress(tabId, chunk, providerName, chunkStartTime);
            }

            const result = await provider.process(chunk, settings);

            // Stop heartbeat when chunk processing completes
            if (providerName === 'google') {
                stopHeartbeatProgress();
            }

            // Note: No timing recording needed - streaming providers are predictable

            // Handle silent failure from the provider (e.g., Google TTS quota met)
            if (result && result.silent_failure) {
                // Stop processing further chunks and exit gracefully
                clearBadge();
                return;
            }

            if (result && result.audioUrl) {
                audioQueue.push({
                    audioUrl: result.audioUrl,
                    text: chunk,
                    alignment: result.alignment
                });
            }
        }



        if (audioQueue.length > 0) {
            // Clear badge and show success
            clearBadge();
            updateBadge('▶', '#4CAF50');
            setTimeout(clearBadge, 2000); // Clear after 2 seconds

            // Send completion message for enhanced progress
            if (providerName === 'google') {
                sendEnhancedProgress(tabId, textChunks.length, textChunks.length, startTime, providerName, 'Processing complete! Starting playback...');
            }

            chrome.tabs.sendMessage(tabId, { type: 'tts-playing' });
            playAudioQueue(audioQueue, tabId, settings.enableHighlighting !== false && !fromClipboard, fromClipboard);
        } else {
            clearBadge();
            chrome.tabs.sendMessage(tabId, { type: 'tts-error', error: 'No audio was generated.' });
        }

    } catch (error) {
        clearBadge();
        updateBadge('!', '#F44336'); // Show error indicator
        setTimeout(clearBadge, 3000); // Clear after 3 seconds

        // Stop heartbeat on error
        stopHeartbeatProgress();

        if (tabId) {
            chrome.tabs.sendMessage(tabId, { type: 'tts-error', error: error.message });
        }
        // Re-throw the error so it can be caught by the caller (e.g., in listeners.js)
        throw error;
    }
}

export async function processTextForDownload(text, format) {
    try {
        const settings = await chrome.storage.sync.get(['selectedProvider', 'elevenLabsApiKey', 'humeApiKey', 'straicoApiKey', 'googleApiKey', 'selectedVoiceId', 'selectedHumeVoiceId', 'selectedHumeVoiceName', 'selectedStraicoVoiceId', 'selectedStraicoModel', 'humePlaybackSpeed', 'playbackSpeed', 'stability', 'similarityBoost', 'style', 'selectedGoogleVoice']);

        // Validate provider selection
        const providerName = settings.selectedProvider;
        if (!providerName) {
            throw new Error('No TTS provider selected. Please select a provider in the extension popup.');
        }

        const provider = providers[providerName];
        if (!provider) {
            throw new Error(`Provider "${providerName}" not found.`);
        }

        // Validate provider-specific requirements
        const validationError = validateProviderSettings(providerName, settings);
        if (validationError) {
            throw new Error(validationError);
        }

        const chunkSize = providerName === 'google' ? 6000 : 3000;
        const sentences = splitIntoSentences(text);
        const textChunks = groupSentencesIntoChunks(sentences, chunkSize);
        const audioBlobs = [];

        // Initialize progress tracking for download
        const totalEstimatedTime = await estimateProcessingTime(text.length, textChunks.length, providerName, text);
        const startTime = Date.now();
        startProgressTimer(totalEstimatedTime, textChunks.length, providerName);

        chrome.runtime.sendMessage({ type: 'conversion-status', status: `processing ${textChunks.length} chunks` }).catch(e => { });

        for (let i = 0; i < textChunks.length; i++) {
            const chunk = textChunks[i];
            const chunkStartTime = Date.now();

            // Update badge progress
            if (i > 0) {
                updateProgressForChunk(i, textChunks.length, startTime);
            }

            // Show special indicator for Google TTS (slower provider)
            if (providerName === 'google') {
                updateBadge(`G${i + 1}/${textChunks.length}`, '#FF9800');
                // Send enhanced progress update for download
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        sendEnhancedProgress(tabs[0].id, i + 1, textChunks.length, startTime, providerName, `Processing chunk ${i + 1} of ${textChunks.length}`);
                        // Start heartbeat for very long chunks in download mode
                        startHeartbeatProgress(tabs[0].id, chunk, providerName, chunkStartTime);
                    }
                });
            }

            chrome.runtime.sendMessage({ type: 'conversion-status', status: `processing chunk ${i + 1} of ${textChunks.length}` }).catch(e => { });
            const result = await provider.process(chunk, settings, format);

            // Stop heartbeat when chunk processing completes
            if (providerName === 'google') {
                stopHeartbeatProgress();
            }

            // Note: No timing recording needed for download - streaming providers are predictable

            if (result && result.audioUrl) {
                const response = await fetch(result.audioUrl);
                const blob = await response.blob();
                audioBlobs.push(blob);
            } else {
                throw new Error(`Audio generation failed for chunk ${i + 1}.`);
            }
        }

        chrome.runtime.sendMessage({ type: 'conversion-status', status: 'combining audio' }).catch(e => { });
        const combinedBlob = new Blob(audioBlobs, { type: 'audio/mpeg' });
        const url = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(combinedBlob);
        });

        let firstThreeWords = text.split(/\s+/).slice(0, 3).join(' ');
        firstThreeWords = firstThreeWords.replace(/[\\/:*?"<>|]/g, '_');
        const fileExtension = format.split('_')[0];
        const filename = `${firstThreeWords}-softreviewed.com.${fileExtension}`;

        chrome.runtime.sendMessage({ type: 'conversion-status', status: 'downloading' }).catch(e => { });
        updateBadge('⬇', '#4CAF50'); // Show download indicator

        await chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: false
        });

        clearBadge();
        updateBadge('✓', '#4CAF50'); // Show success
        setTimeout(clearBadge, 3000); // Clear after 3 seconds

        chrome.runtime.sendMessage({ type: 'conversion-complete' }).catch(e => { });
    } catch (error) {
        clearBadge();
        updateBadge('!', '#F44336'); // Show error indicator
        setTimeout(clearBadge, 3000); // Clear after 3 seconds

        // Stop heartbeat on download error
        stopHeartbeatProgress();

        chrome.runtime.sendMessage({ type: 'conversion-error', error: error.message }).catch(e => { });
    }
}

function sendTimestampsToContentScript(tabId, alignment, text) {
    chrome.tabs.sendMessage(tabId, { type: 'highlight-text', alignment: alignment, text: text }, () => {
        if (chrome.runtime.lastError) {
        }
    });
}
