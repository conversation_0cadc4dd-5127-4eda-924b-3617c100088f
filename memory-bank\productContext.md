# Product Context: TTS Chrome Extension

## Problem Solved
This extension addresses the need for a simple and effective way to listen to web content. It is designed for users who prefer auditory learning, have visual impairments, or want to multitask while consuming content.

## How It Works
The user selects text on a webpage, which triggers a floating "Play" button or a context menu option. The context menu provides useful information like the estimated reading time, word count, and character count. Clicking either the play button or the context menu option sends the selected text to the chosen TTS provider's API. For long texts, the extension automatically splits the text into smaller chunks and plays them sequentially for a seamless experience. The extension then plays the audio back to the user, and for all providers, highlights the words as they are spoken.

## User Experience Goals
- **Simplicity & Minimalism:** The player is designed to be extremely unobtrusive. On text selection, it appears as a small, low-opacity "pill" with only the essential controls: drag, play, and close.
- **Context-Aware UI:** The player reveals complexity only when needed. While playing, hovering over the minimized player expands it to show a full suite of controls, including a horizontal seek bar, provider name, and volume/speed sliders.
- **Informative at a Glance:** In its minimized playing state, the player features a circular progress ring around the play/pause button, allowing users to see the remaining playback time without needing to expand the controls.
- **High-Quality Audio:** By leveraging multiple TTS providers, the extension aims to provide a wide range of natural-sounding voices.
- **Customization:** Users can personalize their experience by choosing from a variety of voices and adjusting the playback speed and volume in the expanded view.
- **Responsive & Draggable:** The player is fully draggable and adjusts its position to remain visible even when the browser window is resized.
- **Dismissible:** A dedicated close button is always present in the minimized view, allowing users to easily dismiss the player.
