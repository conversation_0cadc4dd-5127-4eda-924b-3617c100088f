# System Patterns: TTS Chrome Extension

## System Architecture
The extension is composed of several key components that work together to provide the TTS functionality:

- **`manifest.json`:** Defines the extension's structure, permissions, and entry points.
- **`background.js` (Service Worker):** Acts as the central controller, handling events, API calls, and communication between other components. It also manages the text chunking and audio queuing for long texts.
- **`content.js`:** Injected into web pages to interact with their content. It's responsible for the floating play button and all text highlighting logic.
- **`player.css`:** A dedicated stylesheet injected into web pages to style the floating player. This separates the player's appearance from the extension's logic.
- **`popup.html` & `popup.js`:** Provide the user interface for configuring the extension's settings, such as the API key and voice preferences.
- **`offscreen.html` & `offscreen.js`:** Used for background audio playback and for getting the duration of audio files, as service workers cannot do this directly.

## Design Patterns
- **Shadow DOM for Style Encapsulation:** The floating player is rendered inside a Shadow DOM to prevent CSS conflicts with the host page. The `shadowHost` element is created and styled in `content.js`, and the player's HTML and CSS are injected into the `shadowRoot`.
- **Multi-State Player UI:** The floating player in `content.js` now operates as a sophisticated multi-state component, managed by adding/removing CSS classes on a single container (`#tts-player-container`). This provides a highly responsive and context-aware user experience. The primary states are:
    - **Minimized (Default/Idle):** A small, pill-shaped player with only a drag handle, play button, and close button. Appears on text selection.
    - **Loading:** A compact view showing only a loading spinner, indicating that the TTS audio is being fetched.
    - **Minimized (Playing/Paused):** Same layout as the idle state, but the play/pause button is now wrapped in a circular progress ring that visually tracks audio playback. This is the default view when audio is active.
    - **Expanded (Playing/Paused on Hover):** When the user hovers over the minimized playing/paused player, it expands to reveal the full control set, including a horizontal seek bar, provider name, and volume/speed sliders.
- **Centralized State Management:** The `background.js` script acts as the single source of truth for the current playback state (`playing` or `paused`). This eliminates synchronization issues between the UI and the audio player.
- **Unidirectional Data Flow:** The extension follows a strict unidirectional data flow for playback control. The UI (`content.js`) sends commands to the controller (`background.js`), which then sends commands to the player (`offscreen.js`). The player emits status updates, which are routed back through the controller to the UI. The UI only updates its appearance in response to these messages from the controller.
- **Event-Driven Architecture:** The extension relies heavily on an event-driven model. The background script listens for events such as context menu clicks, messages from other scripts, and browser actions.
- **Message Passing:** Communication between the different components is achieved through a combination of `chrome.runtime.sendMessage` for general events and a persistent `chrome.runtime.connect` port for reliable, direct communication between the background script and the offscreen document. This robust setup prevents the offscreen document from closing prematurely.
- **Separation of Concerns:** Each component has a distinct responsibility. For example, the player's styles are now separated into `player.css`, distinct from the logic in `content.js`, which makes the codebase more modular and maintainable.
- **API Abstraction:** The `background.js` script abstracts the details of the TTS APIs, providing a simple interface for other parts of the extension to use.
- **Asynchronous Text Chunking and Queuing:** To handle long texts, `background.js` splits the text into smaller chunks. It processes the first chunk and immediately begins playback to reduce perceived latency. Subsequent chunks are fetched in the background and added to a queue, ensuring a seamless listening experience without hitting API limits.
- **Estimated Word Highlighting:** For TTS providers that do not support word-level timestamps, `content.js` uses a custom `FreeWordAligner` class to estimate the timing of each word based on syllable counts and punctuation pauses. This provides a good-enough highlighting experience without any external dependencies.
- **Responsive Positioning:** The floating play button's position is dynamically calculated to keep it within the viewport when the window is resized. It also adjusts its position based on the direction of the user's text selection.
- **Clipboard Reading via Content Script Injection:** To read from the clipboard when no text is selected, the background script injects a content script into the active tab. This content script reads the clipboard content and sends it back to the background script for processing. This pattern is used to overcome the limitations of the clipboard API in service workers.
