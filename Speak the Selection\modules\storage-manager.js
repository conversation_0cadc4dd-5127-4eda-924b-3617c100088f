/**
 * Storage Manager Module
 * Centralizes Chrome sync and local storage operations
 * Implements auto-save functionality with visual feedback
 */

export class StorageManager {
    static syncKeys = [
        'selectedProvider', 'elevenLabsApiKey', 'humeApiKey', 'straicoApiKey', 'googleApiKey',
        'selectedVoiceId', 'selectedHumeVoiceId', 'selectedStraicoVoiceId', 'selectedStraicoModel',
        'humePlaybackSpeed', 'straicoPlaybackSpeed', 'showFloatingButton', 'enableHighlighting',
        'filterFree', 'filterLang', 'filterGender', 'playbackSpeed', 'stability', 'similarityBoost',
        'style', 'lastActiveTab', 'selectedGoogleVoice', 'theme'
    ];

    static localKeys = [
        'allVoices', 'allHumeVoices', 'allStraicoVoices', 'ttaText', 'googleRequests', 'disabledSites'
    ];

    /**
     * Load all storage data (sync + local)
     * @returns {Promise<{sync: Object, local: Object}>}
     */
    static async loadAll() {
        const syncPromise = new Promise(resolve =>
            chrome.storage.sync.get(this.syncKeys, resolve)
        );
        const localPromise = new Promise(resolve =>
            chrome.storage.local.get(this.localKeys, resolve)
        );

        const [syncResult, localResult] = await Promise.all([syncPromise, localPromise]);
        return { sync: syncResult, local: localResult };
    }

    /**
     * Save to sync storage
     * @param {Object} data - Data to save
     * @returns {Promise<void>}
     */
    static async saveSync(data) {
        return new Promise(resolve => {
            chrome.storage.sync.set(data, resolve);
        });
    }

    /**
     * Save to local storage
     * @param {Object} data - Data to save
     * @returns {Promise<void>}
     */
    static async saveLocal(data) {
        return new Promise(resolve => {
            chrome.storage.local.set(data, resolve);
        });
    }

    /**
     * Remove from sync storage
     * @param {string|Array<string>} keys - Keys to remove
     * @returns {Promise<void>}
     */
    static async removeSync(keys) {
        return new Promise(resolve => {
            chrome.storage.sync.remove(keys, resolve);
        });
    }

    /**
     * Remove from local storage
     * @param {string|Array<string>} keys - Keys to remove
     * @returns {Promise<void>}
     */
    static async removeLocal(keys) {
        return new Promise(resolve => {
            chrome.storage.local.remove(keys, resolve);
        });
    }

    /**
     * Show auto-save indicator with visual feedback
     * @param {HTMLElement} inputElement - Input element to show indicator next to
     * @param {string} message - Message to display ('Saved' or 'Cleared')
     */
    static showAutoSaveIndicator(inputElement, message) {
        // Remove any existing indicator
        const existingIndicator = inputElement.parentNode.querySelector('.auto-save-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create and show new indicator
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator';
        indicator.textContent = '✓ ' + message;
        indicator.style.cssText = `
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(34, 197, 94, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s ease;
        `;

        // Position the input container relatively
        inputElement.parentNode.style.position = 'relative';
        inputElement.parentNode.appendChild(indicator);

        // Animate in
        setTimeout(() => {
            indicator.style.opacity = '1';
        }, 10);

        // Fade out and remove after 2 seconds
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 200);
        }, 2000);
    }

    /**
     * Debounce utility function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Create auto-save handler for API keys with 500ms debounce
     * @param {HTMLElement} inputElement - Input element
     * @param {string} storageKey - Storage key to use
     * @param {Function} onSave - Optional callback when saved
     * @param {Function} onClear - Optional callback when cleared
     * @returns {Function} Event handler function
     */
    static createAutoSaveHandler(inputElement, storageKey, onSave, onClear) {
        return this.debounce(async () => {
            const value = inputElement.value.trim();

            if (value) {
                await this.saveSync({ [storageKey]: value });
                this.showAutoSaveIndicator(inputElement, 'Saved');
                if (onSave) onSave(value);
            } else {
                await this.removeSync(storageKey);
                this.showAutoSaveIndicator(inputElement, 'Cleared');
                if (onClear) onClear();
            }
        }, 500);
    }

    /**
     * Get current site hostname for disabled sites functionality
     * @returns {Promise<string|null>} Current hostname or null if unable to determine
     */
    static async getCurrentHostname() {
        return new Promise(resolve => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0] && tabs[0].url) {
                    try {
                        const url = new URL(tabs[0].url);
                        resolve(url.hostname);
                    } catch {
                        resolve(null);
                    }
                } else {
                    resolve(null);
                }
            });
        });
    }

    /**
     * Check if extension is enabled on current site
     * @returns {Promise<boolean>} True if enabled, false if disabled
     */
    static async isEnabledOnCurrentSite() {
        const hostname = await this.getCurrentHostname();
        if (!hostname) return true; // Default to enabled if can't determine

        const { local: localData } = await this.loadAll();
        const disabledSites = localData.disabledSites || [];
        return !disabledSites.includes(hostname);
    }

    /**
     * Toggle extension state on current site
     * @param {boolean} enabled - Whether to enable or disable
     * @returns {Promise<void>}
     */
    static async toggleSiteEnabled(enabled) {
        const hostname = await this.getCurrentHostname();
        if (!hostname) return;

        const { local: localData } = await this.loadAll();
        let disabledSites = localData.disabledSites || [];

        if (enabled) {
            // Remove from disabled list
            disabledSites = disabledSites.filter(site => site !== hostname);
        } else {
            // Add to disabled list if not already present
            if (!disabledSites.includes(hostname)) {
                disabledSites.push(hostname);
            }
        }

        await this.saveLocal({ disabledSites });
    }
}