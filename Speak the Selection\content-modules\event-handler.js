/**
 * Event Handler Module
 * Manages document event listeners and user interactions for the TTS extension
 */

import { PlaybackState } from '../shared/constants.js';

export class EventHandler {
    constructor(options = {}) {
        this.shadowDOMManager = options.shadowDOMManager;
        this.audioStateManager = options.audioStateManager;
        this.textSelectionManager = options.textSelectionManager;
        this.highlightingManager = options.highlightingManager;
        this.progressOverlayManager = options.progressOverlayManager;
        this.simpleLoaderManager = options.simpleLoaderManager;
        this.wordAligner = options.wordAligner;

        // State variables
        this.isGloballyDisabled = false;
        this.currentState = PlaybackState.IDLE;
        this.currentRange = null;
        this.anchorToSelection = true;
        this.userHasPositionedPlayer = false;
        this.userActivelyChangedSpeed = false;
        this.currentSessionSpeed = 1.0;
        this.showFloatingButton = true;
        this.initialSelectionRect = null;
    }

    /**
     * Load settings from storage
     */
    async loadSettings() {
        try {
            const settings = await chrome.storage.sync.get(['showFloatingButton']);
            this.showFloatingButton = settings.showFloatingButton !== false; // Default to true
            console.log('Loaded floating button setting:', this.showFloatingButton, 'from storage:', settings.showFloatingButton);
        } catch (error) {
            console.warn('Failed to load settings:', error);
            this.showFloatingButton = true; // Fallback to default
        }
    }

    /**
     * Initialize event handlers
     */
    async initialize() {
        await this.loadSettings();
        this.setupDocumentEvents();
        this.setupMessageListener();
    }

    /**
     * Set up document event listeners
     */
    setupDocumentEvents() {
        // Selection change handler
        document.addEventListener('selectionchange', this.shadowDOMManager.debounce(() => {
            if (this.isGloballyDisabled) return;
            this.updateContextMenu();
        }, 200));

        // Mouse up handler for text selection
        document.addEventListener('mouseup', async (e) => {
            if (this.isGloballyDisabled) return;
            if (!this.showFloatingButton) {
                console.log('Floating button is disabled, skipping text selection handling');
                return;
            }

            const { shadowHost, shadowRoot } = this.shadowDOMManager.getElements();
            if (e.composedPath && shadowRoot && e.composedPath().includes(shadowRoot)) return;

            await this.handleTextSelection(e);
        });

        // Context menu handler
        document.addEventListener('contextmenu', () => {
            if (this.isGloballyDisabled) return;
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                this.currentRange = selection.getRangeAt(0).cloneRange();
                if (this.textSelectionManager) {
                    this.textSelectionManager.setCurrentRange(this.currentRange);
                }
            }
        });

        // Window resize handler
        window.addEventListener('resize', this.shadowDOMManager.debounce(() => {
            this.snapEdges();
        }, 150));
    }

    /**
     * Set up Chrome message listener
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
            // Handle async operations properly
            (async () => {
                try {
                    await this.handleMessage(request, sendResponse);
                } catch (error) {
                    sendResponse({ success: false, error: error.message });
                }
            })();

            return true; // Keep the channel open for async response
        });

        // Listen for storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'sync' && changes.showFloatingButton) {
                this.showFloatingButton = changes.showFloatingButton.newValue !== false;
                console.log('Floating button setting changed:', this.showFloatingButton);
            }
        });
    }

    /**
     * Handle text selection events
     */
    async handleTextSelection(e) {
        // Small delay to ensure selection is stable
        setTimeout(async () => {
            const selection = window.getSelection();

            // If the selection is just a caret (a click), not a drag selection
            if (selection.isCollapsed) {
                // If idle, a click away should hide the player.
                const { shadowHost } = this.shadowDOMManager.getElements();
                if (this.currentState === PlaybackState.IDLE && shadowHost) {
                    shadowHost.style.display = 'none';
                    this.currentRange = null;
                    if (this.textSelectionManager) {
                        this.textSelectionManager.setCurrentRange(null);
                    }
                }
                // If playing or paused, do nothing on a simple click.
                return;
            }

            const text = selection.toString().trim();

            if (text.length > 0) {
                // A new text selection has been made.
                if (this.currentState === PlaybackState.PLAYING || this.currentState === PlaybackState.PAUSED) {
                    try {
                        chrome.runtime.sendMessage({ type: 'pause' });
                    } catch (error) {
                        if (error.message.includes('Extension context invalidated')) {
                            console.warn('Extension context invalidated. Cannot send pause message.');
                        } else {
                            throw error;
                        }
                    }
                    if (this.highlightingManager) {
                        this.highlightingManager.clearHighlights();
                    }
                }

                if (this.currentState === PlaybackState.IDLE) {
                    if (this.highlightingManager) {
                        this.highlightingManager.clearHighlights(); // Clear any old stray highlights
                    }
                }

                // Ensure we have a valid selection before proceeding
                if (selection.rangeCount > 0) {
                    this.currentRange = selection.getRangeAt(0).cloneRange();
                    if (this.textSelectionManager) {
                        this.textSelectionManager.setCurrentRange(this.currentRange);
                    }

                    // Store initial selection position for anchoring
                    const rects = Array.from(this.currentRange.getClientRects());
                    if (rects.length > 0) {
                        this.initialSelectionRect = rects[0];
                    }

                    // For new selections, always position relative to current text location
                    this.anchorToSelection = true;
                    this.userHasPositionedPlayer = false; // Reset user positioning flag for new selection

                    // IMPORTANT: Reset session speed tracking for new selection
                    // This ensures new text always starts at 1.0x default
                    this.userActivelyChangedSpeed = false;
                    this.currentSessionSpeed = 1.0;
                    console.log('New text selection - reset speed to 1.0x default');
                    if (this.audioStateManager) {
                        this.audioStateManager.resetSpeedForNewSelection();
                    }

                    await this.shadowDOMManager.ensurePlayerCreated();
                    const { playerContainer, shadowHost } = this.shadowDOMManager.getElements();

                    if (playerContainer && shadowHost) {
                        this.currentState = PlaybackState.IDLE;
                        playerContainer.className = 'minimized';

                        shadowHost.style.display = 'block'; // NOW show it

                        // FINAL SAFETY: Force speed sync when player becomes visible
                        // This ensures UI always shows correct speed regardless of timing issues
                        setTimeout(() => {
                            if (this.audioStateManager) {
                                this.audioStateManager.syncSpeedWithStorage().catch(() => {
                                    console.warn('Failed to sync speed when player became visible');
                                });
                            }
                        }, 100); // Small delay to ensure UI is fully rendered

                        requestAnimationFrame(() => {
                            this.positionPlayButton();
                        });
                    }
                }
            } else {
                // Selection is not collapsed but has no text (e.g., an image).
                // Treat this like a click away when idle.
                const { shadowHost } = this.shadowDOMManager.getElements();
                if (this.currentState === PlaybackState.IDLE && shadowHost) {
                    shadowHost.style.display = 'none';
                    this.currentRange = null;
                    if (this.textSelectionManager) {
                        this.textSelectionManager.setCurrentRange(null);
                    }
                }
            }
        }, 10);
    }

    /**
     * Handle Chrome extension messages
     */
    async handleMessage(request, sendResponse) {
        const { playerContainer, shadowHost } = this.shadowDOMManager.getElements();

        if (!this.shadowDOMManager.isInitialized() && this.showFloatingButton) {
            await this.shadowDOMManager.ensurePlayerCreated();
        }

        if (!playerContainer && !this.showFloatingButton) {
            // For disabled floating button, we don't need playerContainer
            // Just handle the simple loader case
        } else if (!playerContainer) {
            sendResponse({ success: false, error: 'Player not initialized' });
            return;
        }

        switch (request.type) {
            case 'tts-loading':
                this.currentState = PlaybackState.LOADING;

                if (this.showFloatingButton) {
                    // Ensure player is created for loading state
                    if (!playerContainer) {
                        await this.shadowDOMManager.ensurePlayerCreated();
                    }

                    const { playerContainer, shadowHost } = this.shadowDOMManager.getElements();
                    if (playerContainer && shadowHost) {
                        // Show loading state in player
                        playerContainer.className = 'loading';
                        shadowHost.style.display = 'block';

                        // For clipboard reading, position the player in a default location
                        if (!this.currentRange) {
                            shadowHost.style.position = 'fixed';
                            shadowHost.style.top = '50px';
                            shadowHost.style.right = '50px';
                            shadowHost.style.left = 'auto';
                        }
                    } else {
                        // Fallback to simple loader if player creation failed
                        if (this.simpleLoaderManager) {
                            this.simpleLoaderManager.showSimpleLoader(this.currentRange);
                        }
                    }
                } else {
                    // Show simple loader when floating button is disabled
                    if (this.simpleLoaderManager) {
                        this.simpleLoaderManager.showSimpleLoader(this.currentRange);
                    }
                }

                // For Google TTS, also show enhanced progress for longer operations
                // This will be triggered by enhanced-progress-update messages

                sendResponse({ success: true });
                break;

            case 'tts-playing':
                this.currentState = PlaybackState.PLAYING;
                if (this.showFloatingButton && playerContainer) {
                    playerContainer.className = 'minimized';
                    // CRITICAL: Sync speed when playback starts to ensure UI accuracy
                    if (this.audioStateManager) {
                        this.audioStateManager.syncSpeedWithStorage().catch(() => {
                            console.warn('Failed to sync speed on playback start');
                        });
                    }
                } else {
                    if (this.simpleLoaderManager) {
                        this.simpleLoaderManager.hideSimpleLoader();
                    }
                }
                if (this.progressOverlayManager) {
                    this.progressOverlayManager.hideEnhancedProgress(); // Hide enhanced progress when playing starts
                }
                sendResponse({ success: true });
                break;

            case 'highlight-text':
                this.currentState = PlaybackState.PLAYING;
                if (playerContainer) {
                    playerContainer.className = 'minimized';
                    const { shadowHost } = this.shadowDOMManager.getElements();
                    if (shadowHost) {
                        shadowHost.style.display = 'block';
                        // Sync speed when player becomes visible during highlighting
                        if (this.audioStateManager) {
                            this.audioStateManager.syncSpeedWithStorage().catch(() => {
                                console.warn('Failed to sync speed during highlight-text');
                            });
                        }
                    }
                }
                if (!request.fromClipboard && this.currentRange) {
                    if (this.highlightingManager) {
                        if (request.alignment && request.alignment.characters) {
                            this.highlightingManager.handleHighlighting(request.alignment, request.text);
                        } else if (request.audioDuration && request.text && this.wordAligner) {
                            const aligner = new this.wordAligner(request.text, request.audioDuration);
                            this.highlightingManager.handleFreeHighlighting(aligner.getAlignment(), request.audioDuration, request.text);
                        }
                    }
                } else if (!request.fromClipboard) {
                }
                sendResponse({ success: true });
                break;

            case 'playback-state-changed':
                this.currentState = request.state;
                if (playerContainer) {
                    this.setPlayPauseIcon(request.state === PlaybackState.PLAYING);
                    if (playerContainer._liveRegion) {
                        playerContainer._liveRegion.textContent = `Playback ${request.state}`;
                    }
                    // Sync speed when state changes to playing
                    if (request.state === PlaybackState.PLAYING && this.audioStateManager) {
                        this.audioStateManager.syncSpeedWithStorage().catch(() => {
                            console.warn('Failed to sync speed on state change to playing');
                        });
                    }
                }
                if (request.state === PlaybackState.PLAYING && this.simpleLoaderManager) {
                    this.simpleLoaderManager.hideSimpleLoader();
                }
                if (request.state === PlaybackState.ENDED || request.state === PlaybackState.STOPPED) {
                    this.currentState = PlaybackState.IDLE;
                    if (this.highlightingManager) {
                        this.highlightingManager.clearHighlights();
                    }
                }
                sendResponse({ success: true });
                break;

            case 'tts-error':
                if (this.simpleLoaderManager) {
                    this.simpleLoaderManager.hideSimpleLoader();
                }
                if (this.progressOverlayManager) {
                    this.progressOverlayManager.hideEnhancedProgress(); // Also hide enhanced progress
                }
                this.currentState = PlaybackState.IDLE;
                this.closeHandler();
                sendResponse({ success: true });
                break;

            case 'playback-progress-update':
                if (request.duration > 0) {
                    const fraction = request.currentTime / request.duration;
                    this.setProgressRing(fraction);
                    if (playerContainer) {
                        const slider = playerContainer.querySelector('.tts-progress-slider');
                        const timeLabel = playerContainer.querySelector('.tts-time-label');
                        if (slider) {
                            slider.max = request.duration;
                            slider.value = request.currentTime;
                            playerContainer.style.setProperty('--seek', `${fraction * 100}%`);
                        }
                        if (timeLabel) {
                            timeLabel.textContent = `${this.formatTime(request.currentTime)} / ${this.formatTime(request.duration)}`;
                        }
                    }
                    if (this.highlightingManager) {
                        this.highlightingManager.updateHighlightAtTime(request.currentTime);
                    }
                }
                sendResponse({ success: true });
                break;

            case 'provider-changed':
                this.updatePlayerSettings();
                sendResponse({ success: true });
                break;

            case 'close-player':
                this.closeHandler();
                sendResponse({ success: true });
                break;

            case 'playback-finished':
                // Close player only if the mouse is not currently hovering over it.
                // This avoids closing the player if the user is interacting with it when playback ends.
                if (playerContainer && !playerContainer.matches(':hover')) {
                    this.closeHandler();
                }
                sendResponse({ success: true });
                break;

            case 'site-disabled-status-changed':
                if (request.disabled) {
                    this.isGloballyDisabled = true;
                    this.closeHandler();
                } else {
                    window.location.reload();
                }
                sendResponse({ success: true });
                break;

            case 'enhanced-progress-update':
                if (this.progressOverlayManager) {
                    this.progressOverlayManager.handleEnhancedProgressUpdate(request.data);
                }
                sendResponse({ success: true });
                break;

            case 'heartbeat-progress-update':
                if (this.progressOverlayManager) {
                    this.progressOverlayManager.handleHeartbeatProgressUpdate(request.data);
                }
                sendResponse({ success: true });
                break;

            case 'theme-changed':
                if (this.shadowDOMManager) {
                    const { shadowHost, themeManager } = this.shadowDOMManager.getElements();
                    this.shadowDOMManager.setTheme(request.theme || 'auto');
                    // Re-apply theme to existing player if visible
                    if (playerContainer && shadowHost && shadowHost.style.display !== 'none' && themeManager) {
                        themeManager.autoPositionAndTheme();
                    }
                }
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ success: false, error: 'Unknown message type' });
        }
    }

    /**
     * Update context menu with selected text
     */
    updateContextMenu() {
        const selection = window.getSelection();
        const text = selection.toString().trim();
        if (chrome.runtime && chrome.runtime.id) {
            chrome.runtime.sendMessage({ type: 'update-context-menu', text: text })
                .catch(() => {
                    // Silently ignore if extension context is invalid
                });
        }
    }

    /**
     * Position the play button relative to selected text
     */
    positionPlayButton() {
        const { shadowHost, playerContainer } = this.shadowDOMManager.getElements();
        if (!shadowHost || !playerContainer || !this.currentRange) return;

        // Always position above selected text for new selections
        // Only use saved position if user has manually dragged it AND it's not a new text selection
        try {
            chrome.storage.sync.get('ttsPlayerPos', () => {
                // For now, always auto-position above selected text to fix the positioning issue
                // We can enhance this later to be smarter about when to use saved positions
                const { themeManager } = this.shadowDOMManager.getElements();
                if (themeManager) {
                    themeManager.autoPositionAndTheme();
                }
            });
        } catch (error) {
            if (error.message.includes('Extension context invalidated')) {
                console.warn('Extension context invalidated. Cannot get player position.');
                const { themeManager } = this.shadowDOMManager.getElements();
                if (themeManager) {
                    themeManager.autoPositionAndTheme();
                }
            } else {
                throw error;
            }
        }
    }

    /**
     * Snap player edges to viewport boundaries
     */
    snapEdges() {
        const { shadowHost } = this.shadowDOMManager.getElements();
        if (!shadowHost) return;
        const margin = 12;
        const rect = shadowHost.getBoundingClientRect();
        if (rect.left < margin) shadowHost.style.left = `${margin}px`;
        if (rect.top < margin) shadowHost.style.top = `${margin}px`;
        const vw = window.innerWidth, vh = window.innerHeight;
        if (vw - (rect.left + rect.width) < margin) shadowHost.style.left = `${vw - rect.width - margin}px`;
        if (vh - (rect.top + rect.height) < margin) shadowHost.style.top = `${vh - rect.height - margin}px`;
        this.persistPosition();
    }

    /**
     * Persist player position to storage
     */
    persistPosition() {
        const { shadowHost } = this.shadowDOMManager.getElements();
        if (!shadowHost) return;
        const pos = {
            top: shadowHost.style.top,
            left: shadowHost.style.left,
            width: shadowHost.style.width,
            height: shadowHost.style.height
        };
        try {
            chrome.storage.sync.set({ ttsPlayerPos: pos });
        } catch (error) {
            if (error.message.includes('Extension context invalidated')) {
                console.warn('Extension context invalidated. Cannot persist player position.');
            } else {
                throw error;
            }
        }
    }

    /**
     * Close handler for player cleanup
     */
    closeHandler() {
        try {
            // Stop any ongoing playback
            if (this.currentState !== PlaybackState.IDLE) {
                if (chrome.runtime && chrome.runtime.id) {
                    chrome.runtime.sendMessage({ type: 'pause' }).catch(() => {
                        // Silently ignore if extension context is invalid
                    });
                }
            }

            // Clear highlights and reset state
            if (this.highlightingManager) {
                this.highlightingManager.clearHighlights();
            }
            this.currentState = PlaybackState.IDLE;

            // Hide the player completely
            const { shadowHost } = this.shadowDOMManager.getElements();
            if (shadowHost) {
                shadowHost.style.display = 'none';
            }

            // Reset player to minimized state for next use
            const { playerContainer } = this.shadowDOMManager.getElements();
            if (playerContainer) {
                playerContainer.className = 'minimized';
            }

            // Clear current range so player won't show until new selection
            this.currentRange = null;
            if (this.textSelectionManager) {
                this.textSelectionManager.setCurrentRange(null);
            }
            this.anchorToSelection = true; // Reset anchor behavior for next selection
            this.userHasPositionedPlayer = false; // Reset positioning behavior

            // Reset session speed tracking to ensure next session starts fresh
            this.userActivelyChangedSpeed = false;
            this.currentSessionSpeed = 1.0;
            if (this.audioStateManager) {
                this.audioStateManager.resetSpeedForNewSelection();
            }
            console.log('Player closed - reset session speed tracking');

            // Clear any selection to prevent immediate re-creation
            if (window.getSelection) {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    selection.removeAllRanges();
                }
            }
        } catch (error) {
            // Force hide the player even if other operations fail
            const { shadowHost } = this.shadowDOMManager.getElements();
            if (shadowHost) {
                shadowHost.style.display = 'none';
            }
            this.currentRange = null;
            this.currentState = PlaybackState.IDLE;
        }
    }

    /**
     * Set play/pause icon state
     */
    setPlayPauseIcon(isPlaying) {
        const { playerContainer } = this.shadowDOMManager.getElements();
        if (!playerContainer) return;

        const playPauseButtons = playerContainer.querySelectorAll('.tts-play-pause-button');
        playPauseButtons.forEach(btn => {
            const icon = btn.querySelector('.icon-shape');
            if (icon) {
                icon.setAttribute('d', isPlaying ?
                    'M6 19h4V5H6v14zm8-14v14h4V5h-4z' :
                    'M8 5v14l11-7z');
            }
            btn.setAttribute('data-state', isPlaying ? 'pause' : 'play');
            btn.setAttribute('aria-label', isPlaying ? 'Pause' : 'Play');
        });
    }

    /**
     * Set progress ring visualization
     */
    setProgressRing(fraction) {
        const { playerContainer } = this.shadowDOMManager.getElements();
        if (!playerContainer) return;

        const playPauseButtons = playerContainer.querySelectorAll('.tts-play-pause-button');
        playPauseButtons.forEach(btn => {
            // Remove existing progress ring if any
            let ring = btn.querySelector('.progress-ring');
            if (!ring) {
                ring = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                ring.classList.add('progress-ring');
                ring.setAttribute('cx', '12');
                ring.setAttribute('cy', '12');
                ring.setAttribute('r', '10');
                ring.setAttribute('fill', 'none');
                ring.setAttribute('stroke-width', '2');
                ring.setAttribute('stroke', '#3b82f6');
                ring.style.strokeDasharray = '62.83'; // 2πr = 2π(10) ≈ 62.83
                const svg = btn.querySelector('.icon');
                if (svg) {
                    svg.appendChild(ring);
                }
            }
            ring.style.strokeDashoffset = 62.83 * (1 - fraction);
        });
    }

    /**
     * Format time for display
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Update player settings
     */
    updatePlayerSettings() {
        // This would be implemented based on specific player settings requirements
        console.log('Player settings updated');
    }

    /**
     * Set current state
     */
    setCurrentState(state) {
        this.currentState = state;
    }

    /**
     * Get current state
     */
    getCurrentState() {
        return this.currentState;
    }

    /**
     * Set global disabled state
     */
    setGloballyDisabled(disabled) {
        this.isGloballyDisabled = disabled;
    }

    /**
     * Set show floating button preference
     */
    setShowFloatingButton(show) {
        this.showFloatingButton = show;
    }
}
