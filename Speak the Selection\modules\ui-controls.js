/**
 * UI Controls Module
 * Handles themes, toggles, general settings, and site-specific controls
 */

import { StorageManager } from './storage-manager.js';

export class UIControls {
    constructor(domElements) {
        this.dom = domElements;
    }

    /**
     * Initialize UI controls with event listeners
     */
    initialize() {
        // Theme toggle functionality
        this.dom.themeToggle.addEventListener('click', () => {
            this.cycleTheme();
        });

        // General settings toggles
        this.dom.showFloatingButtonToggle.addEventListener('click', () => {
            const isActive = !this.getToggleState(this.dom.showFloatingButtonToggle);
            this.setToggleState(this.dom.showFloatingButtonToggle, isActive);
            StorageManager.saveSync({ showFloatingButton: isActive });
        });

        this.dom.enableHighlightingToggle.addEventListener('click', () => {
            const isActive = !this.getToggleState(this.dom.enableHighlightingToggle);
            this.setToggleState(this.dom.enableHighlightingToggle, isActive);
            StorageManager.saveSync({ enableHighlighting: isActive });
        });

        // Site-specific enable/disable toggle
        this.dom.disableOnSiteCheckbox.addEventListener('change', async () => {
            const isEnabled = this.dom.disableOnSiteCheckbox.checked;
            await StorageManager.toggleSiteEnabled(isEnabled);

            if (isEnabled) {
                this.dom.generalSettingsWrapper.classList.add('enabled-glow');
            } else {
                this.dom.generalSettingsWrapper.classList.remove('enabled-glow');
            }

            // Notify content script about the change
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        type: 'site-disabled-status-changed',
                        disabled: !isEnabled
                    });
                }
            });
        });
    }

    /**
     * Helper functions for toggle buttons
     */
    setToggleState(toggleElement, isActive) {
        if (isActive) {
            toggleElement.classList.add('active');
        } else {
            toggleElement.classList.remove('active');
        }
    }

    getToggleState(toggleElement) {
        return toggleElement.classList.contains('active');
    }

    /**
     * Theme management functions
     */
    applyTheme(theme) {
        // Remove existing theme classes
        document.body.classList.remove('theme-light', 'theme-dark');

        // Apply theme class based on preference
        if (theme === 'light') {
            document.body.classList.add('theme-light');
        } else if (theme === 'dark') {
            document.body.classList.add('theme-dark');
        }
        // For 'auto', no class is added, so system preference takes effect
    }

    updateThemeToggle(theme) {
        this.dom.themeToggle.setAttribute('data-theme', theme);
        const label = this.dom.themeToggle.querySelector('.theme-label');
        label.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);

        // Apply the theme to the popup
        this.applyTheme(theme);
    }

    async cycleTheme() {
        const currentTheme = this.dom.themeToggle.getAttribute('data-theme');
        let nextTheme;

        switch (currentTheme) {
            case 'auto':
                nextTheme = 'light';
                break;
            case 'light':
                nextTheme = 'dark';
                break;
            case 'dark':
                nextTheme = 'auto';
                break;
            default:
                nextTheme = 'auto';
        }

        this.updateThemeToggle(nextTheme);

        await StorageManager.saveSync({ theme: nextTheme });

        // Notify all tabs about theme change
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'theme-changed',
                    theme: nextTheme
                }).catch(() => {
                    // Ignore errors for tabs that don't have content script
                });
            });
        });
    }

    /**
     * Restore settings from storage
     */
    async restoreSettings(syncData, localData) {
        // Restore theme toggle
        const currentTheme = syncData.theme || 'auto';
        this.updateThemeToggle(currentTheme);

        // Restore general settings toggles
        this.setToggleState(this.dom.showFloatingButtonToggle, syncData.showFloatingButton !== false);
        this.setToggleState(this.dom.enableHighlightingToggle, syncData.enableHighlighting !== false);

        // Restore disabled sites setting
        const isEnabled = await StorageManager.isEnabledOnCurrentSite();
        this.dom.disableOnSiteCheckbox.checked = isEnabled;

        if (isEnabled) {
            this.dom.generalSettingsWrapper.classList.add('enabled-glow');
        } else {
            this.dom.generalSettingsWrapper.classList.remove('enabled-glow');
        }
    }
}