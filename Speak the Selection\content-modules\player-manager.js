/**
 * Player Manager Module
 * Handles player positioning, drag functionality, responsive design, and UI state management
 */

import { TTS_CONFIG } from './tts-config.js';

export class PlayerManager {
    constructor() {
        this.shadowHost = null;
        this.playerContainer = null;
        this.currentRange = null;
        this.anchorToSelection = true;
        this.userHasPositionedPlayer = false;
        this.initialSelectionRect = null;

        // Viewport configuration for responsive positioning
        this.VIEWPORT_CONFIG = {
            mobile: { width: 768, padding: 8, buttonScale: 0.9 },
            tablet: { width: 1024, padding: 12, buttonScale: 0.95 },
            desktop: { width: Infinity, padding: 20, buttonScale: 1.0 }
        };
    }

    /**
     * Initialize with DOM elements
     */
    initialize(shadowHost, playerContainer) {
        this.shadowHost = shadowHost;
        this.playerContainer = playerContainer;
    }

    /**
     * Get viewport configuration based on window width
     */
    getViewportConfig() {
        const width = window.innerWidth;
        if (width <= this.VIEWPORT_CONFIG.mobile.width) return { type: 'mobile', ...this.VIEWPORT_CONFIG.mobile };
        if (width <= this.VIEWPORT_CONFIG.tablet.width) return { type: 'tablet', ...this.VIEWPORT_CONFIG.tablet };
        return { type: 'desktop', ...this.VIEWPORT_CONFIG.desktop };
    }

    /**
     * Position player button relative to current text selection
     */
    positionPlayButton() {
        if (!this.shadowHost || !this.playerContainer || !this.currentRange) return;

        // Always position above selected text for new selections
        // Only use saved position if user has manually dragged it AND it's not a new text selection
        chrome.storage.sync.get('ttsPlayerPos', () => {
            // For now, always auto-position above selected text to fix the positioning issue
            // We can enhance this later to be smarter about when to use saved positions
            this.autoPositionPlayer();
        });
    }

    /**
     * Auto-position player with responsive design
     */
    autoPositionPlayer() {
        if (!this.shadowHost || !this.playerContainer || !this.currentRange) {
            console.log('PlayerManager: Cannot position - missing elements:', {
                shadowHost: !!this.shadowHost,
                playerContainer: !!this.playerContainer,
                currentRange: !!this.currentRange
            });
            return;
        }

        console.log('PlayerManager: Positioning player relative to selection');

        // Get viewport configuration for responsive positioning
        const viewport = this.getViewportConfig();
        const { padding, buttonScale } = viewport;

        // Get fresh rectangles from the current range
        const rects = Array.from(this.currentRange.getClientRects());

        if (rects.length === 0) {
            console.log('PlayerManager: No selection rects, using default position');
            // Default position if no range rects - responsive to viewport
            const defaultTop = Math.min(50, window.innerHeight * 0.1);
            const defaultRight = Math.min(50, window.innerWidth * 0.1);
            this.shadowHost.style.top = `${defaultTop}px`;
            this.shadowHost.style.right = `${defaultRight}px`;
            return;
        }

        // Get player dimensions with responsive scaling
        let buttonHeight = this.playerContainer.offsetHeight;
        let buttonWidth = this.playerContainer.offsetWidth;

        // If dimensions are 0, use fallback values with responsive scaling
        if (buttonHeight === 0 || buttonWidth === 0) {
            const tempStyle = this.shadowHost.style.cssText;
            this.shadowHost.style.cssText = `
                position: fixed !important;
                top: -9999px !important;
                left: -9999px !important;
                visibility: visible !important;
                display: block !important;
                z-index: 2147483647 !important;
                transform: scale(${buttonScale}) !important;
            `;

            this.shadowHost.offsetHeight; // Force reflow
            buttonHeight = (this.playerContainer.offsetHeight || 48) * buttonScale;
            buttonWidth = (this.playerContainer.offsetWidth || 120) * buttonScale;
            this.shadowHost.style.cssText = tempStyle; // Restore original styles
        }

        const firstRect = rects[0];
        const lastRect = rects[rects.length - 1];
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Responsive positioning logic
        let top = firstRect.top - buttonHeight - padding;
        let left = firstRect.left;

        // Smart vertical positioning based on available space
        const spaceAbove = firstRect.top;
        const spaceBelow = viewportHeight - lastRect.bottom;
        const requiredSpace = buttonHeight + padding * 2;

        if (spaceAbove >= requiredSpace) {
            // Position above
            top = firstRect.top - buttonHeight - padding;
        } else if (spaceBelow >= requiredSpace) {
            // Position below
            top = lastRect.bottom + padding;
        } else {
            // Position in largest available space
            if (spaceAbove > spaceBelow) {
                top = Math.max(padding, firstRect.top - buttonHeight - padding);
            } else {
                top = Math.min(viewportHeight - buttonHeight - padding, lastRect.bottom + padding);
            }
        }

        // Smart horizontal positioning
        const selectionCenter = firstRect.left + (firstRect.width / 2);
        left = selectionCenter - (buttonWidth / 2);

        // Responsive horizontal bounds checking
        const minLeft = padding;
        const maxLeft = viewportWidth - buttonWidth - padding;
        left = Math.max(minLeft, Math.min(maxLeft, left));

        // Final responsive bounds check
        top = Math.max(padding, Math.min(viewportHeight - buttonHeight - padding, top));

        // Apply responsive positioning
        this.shadowHost.style.position = 'fixed';
        this.shadowHost.style.top = `${top}px`;
        this.shadowHost.style.left = `${left}px`;
        this.shadowHost.style.width = `${buttonWidth}px`;
        this.shadowHost.style.height = `${buttonHeight}px`;
        this.shadowHost.style.minWidth = `${buttonWidth}px`;
        this.shadowHost.style.minHeight = `${buttonHeight}px`;
        this.shadowHost.style.maxWidth = 'none';
        this.shadowHost.style.maxHeight = 'none';
        this.shadowHost.style.transform = `scale(${buttonScale})`;

        console.log('PlayerManager: Positioned player at:', {
            top: `${top}px`,
            left: `${left}px`,
            selectionRect: firstRect,
            viewport: { width: viewportWidth, height: viewportHeight }
        });

        // Set anchor direction for styling
        const above = parseInt(this.shadowHost.style.top) + buttonHeight < lastRect.top;
        this.playerContainer.setAttribute('data-anchor', above ? 'down' : 'up');
        this.playerContainer.setAttribute('data-viewport', viewport.type);
    }

    /**
     * Enable drag functionality for player
     */
    enableDrag(handleEl, containerEl) {
        if (!handleEl || !containerEl) return;
        let startX = 0, startY = 0, isDragging = false;

        const onPointerDown = (e) => {
            isDragging = false;
            handleEl.setPointerCapture(e.pointerId);
            startX = e.clientX;
            startY = e.clientY;
            window.addEventListener('pointermove', onPointerMove);
            window.addEventListener('pointerup', onPointerUp, { once: true });
        };

        const onPointerMove = (e) => {
            const dx = e.clientX - startX;
            const dy = e.clientY - startY;

            if (!isDragging && (Math.abs(dx) > 3 || Math.abs(dy) > 3)) {
                isDragging = true;
                containerEl.style.transition = 'none';
                this.anchorToSelection = false;
            }

            if (isDragging) {
                e.preventDefault();
                containerEl.style.transform = `translate(${dx}px, ${dy}px)`;
            }
        };

        const onPointerUp = (e) => {
            handleEl.releasePointerCapture(e.pointerId);
            window.removeEventListener('pointermove', onPointerMove);

            if (isDragging) {
                e.preventDefault();
                containerEl.style.transition = '';
                const finalRect = containerEl.getBoundingClientRect();
                const newLeft = finalRect.left;
                const newTop = finalRect.top;
                const clampedLeft = Math.max(10, Math.min(window.innerWidth - containerEl.offsetWidth - 10, newLeft));
                const clampedTop = Math.max(10, Math.min(window.innerHeight - containerEl.offsetHeight - 10, newTop));
                containerEl.style.transform = '';
                containerEl.style.left = `${clampedLeft}px`;
                containerEl.style.top = `${clampedTop}px`;
                this.userHasPositionedPlayer = true;
                this.persistPosition();
            }
        };

        handleEl.addEventListener('pointerdown', onPointerDown);
    }

    /**
     * Snap player to viewport edges if outside bounds
     */
    snapEdges() {
        if (!this.shadowHost) return;
        const margin = 12;
        const rect = this.shadowHost.getBoundingClientRect();
        if (rect.left < margin) this.shadowHost.style.left = `${margin}px`;
        if (rect.top < margin) this.shadowHost.style.top = `${margin}px`;
        const vw = window.innerWidth, vh = window.innerHeight;
        if (vw - (rect.left + rect.width) < margin) this.shadowHost.style.left = `${vw - rect.width - margin}px`;
        if (vh - (rect.top + rect.height) < margin) this.shadowHost.style.top = `${vh - rect.height - margin}px`;
        this.persistPosition();
    }

    /**
     * Persist player position to storage
     */
    persistPosition() {
        if (!this.shadowHost) return;
        const rect = this.shadowHost.getBoundingClientRect();
        chrome.storage.sync.set({ ttsPlayerPos: { left: rect.left, top: rect.top } });
    }

    /**
     * Restore player position from storage
     */
    restorePosition() {
        chrome.storage.sync.get('ttsPlayerPos', (r) => {
            const pos = r.ttsPlayerPos;
            if (!pos) return;
            this.shadowHost.style.left = `${pos.left}px`;
            this.shadowHost.style.top = `${pos.top}px`;
            this.userHasPositionedPlayer = true;
        });
    }

    /**
     * Create repositioning handlers with debouncing
     */
    createRepositionHandlers() {
        // Reposition on scroll/resize when anchored to selection
        const repositionIfAnchored = this.debounce(() => {
            try {
                if (!this.anchorToSelection) return;
                if (!this.shadowHost || !this.playerContainer || !this.currentRange) return;
                if (this.shadowHost.style.display === 'none') return;
                if (this.shadowHost.matches(':hover')) return;

                // Always reposition when anchored to maintain position relative to selected text
                this.autoPositionPlayer();
            } catch (e) {
                // Silently ignore errors during repositioning
            }
        }, 50); // Reduced debounce for more responsive positioning

        // Enhanced repositioning for resize and orientation changes
        const repositionOnResize = this.debounce(() => {
            try {
                if (!this.shadowHost || !this.playerContainer) return;
                if (this.shadowHost.style.display === 'none') return;

                // For resize/orientation changes, always reposition regardless of anchor state
                // This ensures the player stays visible and properly positioned
                if (this.currentRange) {
                    this.autoPositionPlayer();
                } else {
                    // If no current range, just ensure it stays within viewport bounds
                    this.snapEdges();
                }
            } catch (e) {
                // Silently ignore errors during repositioning
            }
        }, 100); // Slightly longer debounce for resize events

        // Capture scroll events from any scrollable ancestor with capture phase
        window.addEventListener('scroll', repositionIfAnchored, { capture: true, passive: true });
        document.addEventListener('scroll', repositionIfAnchored, { capture: true, passive: true });

        // Enhanced resize and orientation change handling
        window.addEventListener('resize', repositionOnResize, { passive: true });
        window.addEventListener('orientationchange', () => {
            // Use setTimeout to allow orientation change to complete
            setTimeout(repositionOnResize, 200);
        }, { passive: true });

        // Additional viewport change listeners for better responsiveness
        window.addEventListener('visualViewport-resize', repositionOnResize, { passive: true });

        // Listen for fullscreen changes that might affect positioning
        document.addEventListener('fullscreenchange', repositionOnResize, { passive: true });

        // Also handle window resize with edge snapping
        window.addEventListener('resize', this.debounce(() => this.snapEdges(), 150));
    }

    /**
     * Update slider backgrounds with theme awareness
     */
    updateVolumeSliderBackground(slider, volume) {
        // Volume slider range is 0 to 1, so we can directly use volume * 100
        const volumePercentage = Math.max(0, Math.min(100, volume * 100));
        slider.style.background = `linear-gradient(to top, var(--progress-fill, #60a5fa) ${volumePercentage}%, var(--progress-track, rgba(255, 255, 255, 0.16)) ${volumePercentage}%)`;
    }

    updateSpeedSliderBackground(slider, speed) {
        // Speed slider range is 0.5 to 2.0, so we need to normalize this to 0-100%
        const speedPercentage = Math.max(0, Math.min(100, ((speed - 0.5) / (2.0 - 0.5)) * 100));
        slider.style.background = `linear-gradient(to top, var(--progress-fill, #60a5fa) ${speedPercentage}%, var(--progress-track, rgba(255, 255, 255, 0.16)) ${speedPercentage}%)`;
    }

    /**
     * Set current range for positioning calculations
     */
    setCurrentRange(range) {
        this.currentRange = range;

        // Store initial selection position for anchoring
        if (range) {
            const rects = Array.from(range.getClientRects());
            if (rects.length > 0) {
                this.initialSelectionRect = rects[0];
            }
        }
    }

    /**
     * Reset positioning state for new selections
     */
    resetForNewSelection() {
        // For new selections, always position relative to current text location
        this.anchorToSelection = true;
        this.userHasPositionedPlayer = false; // Reset user positioning flag for new selection
    }

    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle utility function
     */
    throttle(func, limit) {
        let inThrottle;
        return function (...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Get current positioning state
     */
    getPositioningState() {
        return {
            anchorToSelection: this.anchorToSelection,
            userHasPositionedPlayer: this.userHasPositionedPlayer,
            initialSelectionRect: this.initialSelectionRect
        };
    }

    /**
     * Update positioning state
     */
    setPositioningState(state) {
        if (state.anchorToSelection !== undefined) {
            this.anchorToSelection = state.anchorToSelection;
        }
        if (state.userHasPositionedPlayer !== undefined) {
            this.userHasPositionedPlayer = state.userHasPositionedPlayer;
        }
        if (state.initialSelectionRect !== undefined) {
            this.initialSelectionRect = state.initialSelectionRect;
        }
    }
}