import { processTextForTTS, processTextForDownload } from './tts.js';
import { pauseAudio, resumeAudio, setVolume, setSpeed, seekAudio, setOffscreenPort, getOffscreenPort, setupOffscreenDocument, getActualAudioState } from './audio.js';
import { PlaybackState } from '../shared/constants.js';
import { google } from './google-tts.js';

let ttsTabId = null;
let playbackState = PlaybackState.IDLE;

// Badge management functions
function clearBadge() {
    chrome.action.setBadgeText({ text: '' });
}

export function initializeListeners() {
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: () => {
                    const scriptUrl = chrome.runtime.getURL('content.js');
                    import(scriptUrl).catch(err => console.error("Error importing content script:", err));
                }
            }).catch(err => console.error('Failed to inject content script:', err));
        }
    });

    chrome.runtime.onInstalled.addListener(async (details) => {
        if (details.reason === 'install') {
            // Set a default value for the Straico model on first installation
            chrome.storage.sync.set({ selectedStraicoModel: 'tts-1', volume: 1 });
        }
        // Create the main context menu item
        chrome.contextMenus.create({
            id: 'read-aloud',
            title: 'Read aloud',
            contexts: ['all']
        });

        // Initialize Google TTS quota reset alarm
        await setupGoogleTTSQuotaReset();
    });

    // Setup automatic midnight reset for Google TTS quota
    async function setupGoogleTTSQuotaReset() {
        try {
            // Clear any existing alarm
            await chrome.alarms.clear('googleTTSQuotaReset');

            // Calculate next midnight Pacific Time
            const nextMidnight = google.getNextMidnightPacific();
            const now = Date.now();
            const minutesUntilMidnight = (nextMidnight - now) / (1000 * 60);

            // Create alarm for next midnight Pacific Time
            await chrome.alarms.create('googleTTSQuotaReset', {
                when: nextMidnight,
                periodInMinutes: 24 * 60 // Repeat every 24 hours
            });

        } catch (error) {
        }
    }

    // Handle alarm events
    chrome.alarms.onAlarm.addListener(async (alarm) => {
        if (alarm.name === 'googleTTSQuotaReset') {
            try {
                await google.resetDailyQuota();
            } catch (error) {
            }
        }
    });

    // Also setup the alarm when the service worker starts up
    setupGoogleTTSQuotaReset();

    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
        try {
            if (info.menuItemId === 'read-aloud') {
                if (info.selectionText) {

                    // Send loading state to content script first
                    chrome.tabs.sendMessage(tab.id, { type: 'tts-loading' });
                    processTextForTTS(info.selectionText, tab.id, false).catch(err => {
                        chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            func: (message) => alert(message),
                            args: [err.message || 'An unknown error occurred.']
                        });
                    });
                } else {
                    chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        function: getClipboardText,
                    });
                }
            }
        } catch (error) {
        }
    });

    chrome.runtime.onConnect.addListener((port) => {
        if (port.name === 'offscreen') {
            setOffscreenPort(port);
            port.onMessage.addListener(async (msg) => {
                try {
                    const { ttsTabId: storedTabId } = await chrome.storage.session.get('ttsTabId');
                    const effectiveTabId = storedTabId || ttsTabId;

                    if (!effectiveTabId) {
                        return;
                    }

                    // Forward the message to the content script
                    chrome.tabs.sendMessage(effectiveTabId, msg, (response) => {
                        if (chrome.runtime.lastError) {
                            // Error sending message, tab might be closed.
                        }
                    });

                    if (msg.type === 'playback-state-changed' && msg.state === 'ended') {
                        // This is now handled by the promise-based loop in tts.js
                    }
                    if (msg.type === 'playback-finished') {
                        clearBadge(); // Clear badge when playback finishes
                        chrome.tabs.sendMessage(effectiveTabId, { type: 'close-player' });
                    }
                    if (msg.type === 'clipboard-text-response') {
                        if (msg.text) {
                            processTextForTTS(msg.text, effectiveTabId, true).catch(err => { });
                        }
                    }
                } catch (error) {
                }
            });

            port.onDisconnect.addListener(() => {
                // This is now handled in audio.js
            });
        }
    });

    function getClipboardText() {
        navigator.clipboard.readText().then(text => {
            chrome.runtime.sendMessage({ type: 'clipboard-text-response', text: text });
        });
    }

    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        (async () => {
            try {
                if (request.type === 'clipboard-text-response') {
                    if (request.text) {
                        processTextForTTS(request.text, sender.tab.id, true).catch(err => { });
                    }
                    return;
                }
                if (request.type === 'read-text' && sender.tab) {
                    ttsTabId = sender.tab.id;
                }

                if (request.type === 'update-context-menu') {
                    if (request.text) {
                        const charCount = request.text.length;
                        const wordCount = request.text.split(/\s+/).filter(Boolean).length;

                        // Calculate precise reading time (200 words per minute average)
                        const totalSeconds = Math.round((wordCount / 200) * 60);
                        const minutes = Math.floor(totalSeconds / 60);
                        const seconds = totalSeconds % 60;

                        let readingTimeText;
                        if (minutes > 0 && seconds > 0) {
                            readingTimeText = `${minutes}m ${seconds}s`;
                        } else if (minutes > 0) {
                            readingTimeText = `${minutes}m`;
                        } else {
                            readingTimeText = `${seconds}s`;
                        }

                        await chrome.contextMenus.update('read-aloud', {
                            title: `Read aloud (${readingTimeText} read, ${wordCount} words, ${charCount} chars)`,
                            enabled: true
                        });
                    } else {
                        await chrome.contextMenus.update('read-aloud', {
                            title: 'Read aloud',
                            enabled: true
                        });
                    }
                    sendResponse({ success: true });
                    return;
                }

                switch (request.type) {
                    case 'get-playback-state':
                        sendResponse({ playbackState: PlaybackState });
                        break;
                    case 'get-current-audio-state':
                        // Get the actual audio state from offscreen document
                        try {
                            const actualState = await getActualAudioState();
                            const mappedState = actualState.isPlaying ? PlaybackState.PLAYING :
                                actualState.state === 'paused' ? PlaybackState.PAUSED :
                                    actualState.state === 'ended' ? PlaybackState.ENDED :
                                        PlaybackState.IDLE;
                            sendResponse({
                                state: mappedState,
                                currentTime: actualState.currentTime,
                                duration: actualState.duration,
                                isPlaying: actualState.isPlaying
                            });
                        } catch (error) {
                            sendResponse({ state: playbackState });
                        }
                        break;
                    case 'get-player-css':
                        const playerCssUrl = chrome.runtime.getURL('player.css');
                        const loadingCssUrl = chrome.runtime.getURL('loading.css');
                        const [playerCss, loadingCss] = await Promise.all([
                            fetch(playerCssUrl).then(res => res.ok ? res.text() : Promise.reject(`Failed to load player.css: ${res.statusText}`)),
                            fetch(loadingCssUrl).then(res => res.ok ? res.text() : Promise.reject(`Failed to load loading.css: ${res.statusText}`))
                        ]);
                        sendResponse({ playerCss, loadingCss });
                        break;
                    case 'read-text':
                        playbackState = PlaybackState.LOADING;
                        // Send loading state to content script
                        chrome.tabs.sendMessage(sender.tab.id, { type: 'tts-loading' });
                        processTextForTTS(request.text, sender.tab.id).catch(err => {
                            chrome.scripting.executeScript({
                                target: { tabId: sender.tab.id },
                                func: (message) => alert(message),
                                args: [err.message || 'An unknown error occurred.']
                            });
                        });
                        sendResponse({ success: true });
                        break;
                    case 'pause':
                        playbackState = PlaybackState.PAUSED;
                        await pauseAudio();
                        sendResponse({ success: true });
                        break;
                    case 'resume':
                        playbackState = PlaybackState.PLAYING;
                        await resumeAudio();
                        sendResponse({ success: true });
                        break;
                    case 'set-volume':
                        await setVolume(request.volume);
                        sendResponse({ success: true });
                        break;
                    case 'set-speed':
                        await setSpeed(request.speed);
                        sendResponse({ success: true });
                        break;
                    case 'seek-audio':
                        await seekAudio(request.time);
                        sendResponse({ success: true });
                        break;
                    case 'convert-text-to-audio':
                        await processTextForDownload(request.text, request.format);
                        sendResponse({ success: true });
                        break;
                }
            } catch (error) {
                // Optionally send an error response
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true; // Indicate asynchronous response
    });
}
