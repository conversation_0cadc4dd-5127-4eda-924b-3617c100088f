# Speak the Selection — Read Aloud with AI Voices 🗣️

Transform your reading experience with **Speak the Selection**, the ultimate text-to-speech (TTS) extension that brings web content to life. Simply highlight any text, and our extension will read it aloud in natural, high-quality AI voices from the industry's leading providers: **Google, ElevenLabs, Hume, and Straico**.

This is the perfect tool for anyone who wants to listen to articles, documents, or any web content. Whether you're multitasking, have a visual impairment, or simply prefer auditory learning, Speak the Selection makes it easy to consume information without being tied to your screen.

## Why Choose Speak the Selection?

-   **🚀 Your Keys, Your Control:** Unlike other TTS extensions, we don't charge you a subscription or act as a middleman. You connect your own API keys directly to Google, ElevenLabs, Hume, and Straico. This means you get full control and can take advantage of each provider's generous **free tiers**. When you exhaust the free credits from one, simply switch to another!

-   **💎 Premium Voices for Free:** Access the same cutting-edge, natural-sounding AI voices that power professional applications. Why settle for robotic narration when you can listen to content read by the best?

-   **✨ Seamless and Intuitive:** Our clean, modern interface is designed to be there when you need it and out of the way when you don't.

## Feature-Packed for a Superior Experience

-   **🌐 Multi-Provider Support:** Choose from a diverse range of voices and languages from Google, ElevenLabs, Hume, and Straico.
-   **🚫 Disable on Selected Websites:** If you don't want the extension to run on certain sites, simply click the button to disable it for those websites—giving you full control over where it works.
-   **▶️ Instant Playback:** A sleek, floating play button appears as soon as you select text. It's draggable, so you can place it anywhere on the screen.
-   **📋 Read from Clipboard:** Don't want to select text? No problem! Our context menu can read directly from your clipboard.
-   **🖱️ Context Menu Integration:** Prefer a right-click? "Read Aloud" is also available in your context menu, along with handy stats.
-   **📊 Real-Time Info:** The context menu shows you the character count, word count, and estimated reading time before you even start.
-   **✍️ Word-for-Word Highlighting:** Follow along with synchronized, word-by-word highlighting that works with all our supported providers.
-   **🎛️ Customizable Player:** The floating player includes easy-to-use controls for play/pause, volume, and playback speed.
-   **📱 Smart and Responsive:** The player automatically adjusts its position to stay visible, even when you resize your browser window.
-   **🎵 Uninterrupted Audio:** Our extension uses an offscreen document to ensure your audio continues playing in the background, even if you switch tabs.
-   **📚 Long Text, No Problem:** Long articles are automatically split into smaller chunks for smooth, uninterrupted playback.
-   **💰 Credit Saver:** For very long selections, we'll show you a confirmation prompt to prevent accidental high credit usage.
-   **🎤 Text-to-Audio Converter:** Have a block of text you want to save as an audio file? Just paste it into our converter, and you can download it in MP3 or WAV format.

## Use Cases for Everyone

-   **🎓 Students:** Listen to research papers, articles, and textbooks to make studying more efficient. A great tool for auditory learners and those with reading disabilities like dyslexia.
-   **👨‍💻 Professionals:** Keep up with industry news and reports while you work on other tasks.
-   **📰 Content Consumers:** Breeze through your daily news, blogs, and long-form articles without the eye strain.
-   **Accessibility:** An invaluable tool for users with visual impairments, providing a clear and natural-sounding way to browse the web.
-   **✍️ Writers & Editors:** Proofread your work by listening to it, making it easier to catch errors and awkward phrasing.

## Get Started in Seconds

1.  Install the extension.
2.  Open the settings and enter your API key for Google, ElevenLabs, Hume, or Straico. (You can get these for free from their respective websites.)
3.  Select your favorite voice.
4.  Highlight any text on any website and press play!

---

## Permission Justifications

-   **Alarms:** This permission is used to schedule a daily task that resets your Google TTS free quota count. This ensures that your usage is accurately tracked each day, giving you reliable access to the free tier.
-   **Clipboard Read:** This permission is required for the "Read from Clipboard" feature. It allows the extension to read the text you have copied so it can be converted to speech, but only when you explicitly trigger it from the context menu. Your clipboard data is never stored or used for any other purpose.

---

## Privacy & Data Handling

This extension is built to respect your privacy. Here’s what you need to know about your data:

-   **Data Collection:** The only data this extension handles is the **Website Content** (the text you highlight or copy to your clipboard) that you want to hear read aloud.
-   **No Personal Information:** We do **not** collect, store, or transmit any personally identifiable information, web history, or user activity.
-   **Third-Party Services:** The selected text is sent directly to the TTS provider you have chosen (e.g., Google, ElevenLabs). Your API keys for these services are stored locally on your device and are only used to authenticate with these providers. We recommend reviewing the privacy policies of your chosen TTS providers to understand how they handle data.
-   **Local Storage:** All settings, including your API keys, are stored locally in your browser's storage and are never sent to any server other than the TTS provider you are using.

---

*Keywords: text to speech, tts, read aloud, text reader, screen reader, natural voices, ai voice, google, elevenlabs, hume, straico, read text, speech synthesis, accessibility, productivity, audio reader, listen to articles, dyslexia, visual impairment, proofreading, read my screen, text to voice, text to mp3, clipboard reader, read from clipboard*
