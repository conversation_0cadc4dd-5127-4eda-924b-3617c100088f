/**
 * Text Selection Manager Module
 * Handles text selection, range management, context menu updates, and selection event handling
 */

export class TextSelectionManager {
    constructor() {
        this.currentRange = null;
        this.isGloballyDisabled = false;
        this.showFloatingButton = true;
        this.shadowRoot = null;

        // Callbacks
        this.onSelectionChange = null;
        this.onNewSelection = null;
        this.onSelectionCleared = null;
    }

    /**
     * Initialize the text selection manager
     */
    initialize(config = {}) {
        this.isGloballyDisabled = config.isGloballyDisabled || false;
        this.showFloatingButton = config.showFloatingButton !== false;
        this.shadowRoot = config.shadowRoot || null;

        this.setupEventListeners();
    }

    /**
     * Set up event listeners for selection and context menu
     */
    setupEventListeners() {
        // Selection change event with debouncing
        document.addEventListener('selectionchange', this.debounce(() => {
            if (this.isGloballyDisabled) return;
            this.updateContextMenu();
        }, 200));

        // Mouse up event for text selection handling
        document.addEventListener('mouseup', async (e) => {
            if (this.isGloballyDisabled) return;
            if (!this.showFloatingButton) return;
            if (e.composedPath && this.shadowRoot && e.composedPath().includes(this.shadowRoot)) return;

            // Small delay to ensure selection is stable
            setTimeout(async () => {
                await this.handleMouseUpSelection(e);
            }, 10);
        });

        // Context menu event
        document.addEventListener('contextmenu', () => {
            if (this.isGloballyDisabled) return;
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                this.currentRange = selection.getRangeAt(0).cloneRange();
            }
        });
    }

    /**
     * Handle mouse up selection events
     */
    async handleMouseUpSelection(e) {
        const selection = window.getSelection();

        // If the selection is just a caret (a click), not a drag selection
        if (selection.isCollapsed) {
            // If idle, a click away should hide the player
            if (this.onSelectionCleared) {
                this.onSelectionCleared();
            }
            return;
        }

        const text = selection.toString().trim();

        if (text.length > 0) {
            // A new text selection has been made
            if (selection.rangeCount > 0) {
                this.currentRange = selection.getRangeAt(0).cloneRange();

                // Store initial selection position for anchoring
                const rects = Array.from(this.currentRange.getClientRects());
                const initialSelectionRect = rects.length > 0 ? rects[0] : null;

                // Notify about new selection
                if (this.onNewSelection) {
                    await this.onNewSelection({
                        range: this.currentRange,
                        text: text,
                        initialRect: initialSelectionRect
                    });
                }
            }
        } else {
            // Selection is not collapsed but has no text (e.g., an image)
            // Treat this like a click away when idle
            if (this.onSelectionCleared) {
                this.onSelectionCleared();
            }
        }
    }

    /**
     * Update context menu with current selection
     */
    updateContextMenu() {
        const selection = window.getSelection();
        const text = selection.toString().trim();

        if (chrome.runtime && chrome.runtime.id) {
            chrome.runtime.sendMessage({ type: 'update-context-menu', text: text })
                .catch(() => {
                    // Silently ignore if extension context is invalid
                });
        }

        // Notify about selection change
        if (this.onSelectionChange) {
            this.onSelectionChange({ text, selection });
        }
    }

    /**
     * Get text from current range
     */
    getCurrentSelectionText() {
        if (!this.currentRange) return '';
        return this.currentRange.toString().trim();
    }

    /**
     * Get rectangles from current range
     */
    getCurrentSelectionRects() {
        if (!this.currentRange) return [];
        return Array.from(this.currentRange.getClientRects());
    }

    /**
     * Clear current selection and range
     */
    clearSelection() {
        if (window.getSelection) {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                selection.removeAllRanges();
            }
        }
        this.currentRange = null;
    }

    /**
     * Set current range
     */
    setCurrentRange(range) {
        this.currentRange = range;
    }

    /**
     * Get current range
     */
    getCurrentRange() {
        return this.currentRange;
    }

    /**
     * Check if there's an active selection
     */
    hasActiveSelection() {
        return this.currentRange !== null;
    }

    /**
     * Validate selection text length
     */
    isValidSelectionLength(maxLength = 4000) {
        const text = this.getCurrentSelectionText();
        return text.length > 0 && text.length <= maxLength;
    }

    /**
     * Get selection confirmation for long text
     */
    confirmLongSelection(maxLength = 4000) {
        const text = this.getCurrentSelectionText();
        if (text.length > maxLength) {
            return confirm('The selected text is very long... proceed?');
        }
        return true;
    }

    /**
     * Find word rectangle in range for highlighting
     */
    findWordRectInRange(range, targetWord, targetCharStart) {
        try {
            const rangeText = range.toString();
            const actualStartIndex = rangeText.indexOf(targetWord, targetCharStart);

            if (actualStartIndex === -1) {
                return null;
            }

            // Create a new range for just this word
            const wordRange = document.createRange();

            // Find the text node and offset for the start position
            let currentPos = 0;
            const walker = document.createTreeWalker(
                range.commonAncestorContainer,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                if (!range.intersectsNode(node)) continue;

                let nodeText = node.textContent;
                let nodeStart = currentPos;

                // Adjust for range boundaries
                if (node === range.startContainer) {
                    nodeText = nodeText.substring(range.startOffset);
                    nodeStart = currentPos;
                }
                if (node === range.endContainer) {
                    const endOffset = node === range.startContainer ?
                        range.endOffset - range.startOffset :
                        range.endOffset;
                    nodeText = nodeText.substring(0, endOffset);
                }

                const nodeEnd = currentPos + nodeText.length;

                if (actualStartIndex >= nodeStart && actualStartIndex < nodeEnd) {
                    // Found the node containing our word start
                    const localOffset = (actualStartIndex - nodeStart) +
                        (node === range.startContainer ? range.startOffset : 0);
                    const wordEnd = Math.min(
                        localOffset + targetWord.length,
                        node.textContent.length
                    );

                    wordRange.setStart(node, localOffset);
                    wordRange.setEnd(node, wordEnd);

                    const rect = wordRange.getBoundingClientRect();

                    if (rect.width > 0 && rect.height > 0) {
                        return rect;
                    }
                }

                currentPos = nodeEnd;
            }

        } catch (e) {
            // Silently ignore errors
        }

        return null;
    }

    /**
     * Get range boundaries info
     */
    getRangeBoundaries() {
        if (!this.currentRange) return null;

        return {
            startContainer: this.currentRange.startContainer,
            startOffset: this.currentRange.startOffset,
            endContainer: this.currentRange.endContainer,
            endOffset: this.currentRange.endOffset,
            commonAncestorContainer: this.currentRange.commonAncestorContainer
        };
    }

    /**
     * Clone current range
     */
    cloneCurrentRange() {
        return this.currentRange ? this.currentRange.cloneRange() : null;
    }

    /**
     * Check if range intersects with a node
     */
    rangeIntersectsNode(node) {
        return this.currentRange ? this.currentRange.intersectsNode(node) : false;
    }

    /**
     * Set callback functions
     */
    setCallbacks(callbacks) {
        if (callbacks.onSelectionChange) {
            this.onSelectionChange = callbacks.onSelectionChange;
        }
        if (callbacks.onNewSelection) {
            this.onNewSelection = callbacks.onNewSelection;
        }
        if (callbacks.onSelectionCleared) {
            this.onSelectionCleared = callbacks.onSelectionCleared;
        }
    }

    /**
     * Update configuration
     */
    updateConfig(config) {
        if (config.isGloballyDisabled !== undefined) {
            this.isGloballyDisabled = config.isGloballyDisabled;
        }
        if (config.showFloatingButton !== undefined) {
            this.showFloatingButton = config.showFloatingButton;
        }
        if (config.shadowRoot !== undefined) {
            this.shadowRoot = config.shadowRoot;
        }
    }

    /**
     * Get selection stats
     */
    getSelectionStats() {
        const text = this.getCurrentSelectionText();
        const rects = this.getCurrentSelectionRects();

        return {
            hasSelection: this.hasActiveSelection(),
            textLength: text.length,
            text: text,
            rectCount: rects.length,
            firstRect: rects.length > 0 ? rects[0] : null,
            lastRect: rects.length > 0 ? rects[rects.length - 1] : null
        };
    }

    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}