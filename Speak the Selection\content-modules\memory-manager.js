/**
 * Memory Manager Module
 * Handles advanced cleanup and resource management for the TTS extension
 */

export class MemoryManager {
    static timers = new Set();
    static intervals = new Set();
    static eventListeners = new Map();
    static debouncedFunctions = new Map();

    /**
     * Track a timer for cleanup
     * @param {number} timerId - Timer ID from setTimeout
     * @returns {number} The timer ID
     */
    static trackTimer(timerId) {
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * Track an interval for cleanup
     * @param {number} intervalId - Interval ID from setInterval
     * @returns {number} The interval ID
     */
    static trackInterval(intervalId) {
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * Track an event listener for cleanup
     * @param {Element} element - DOM element
     * @param {string} event - Event type
     * @param {Function} handler - Event handler function
     * @param {Object} options - Event listener options
     */
    static trackEventListener(element, event, handler, options) {
        const key = `${element.tagName || 'unknown'}-${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        this.eventListeners.get(key).push({ element, event, handler, options });
        element.addEventListener(event, handler, options);
    }

    /**
     * Track a debounced function timeout
     * @param {string} name - Function name
     * @param {number} timeoutId - Timeout ID
     */
    static trackDebouncedFunction(name, timeoutId) {
        if (this.debouncedFunctions.has(name)) {
            clearTimeout(this.debouncedFunctions.get(name));
        }
        this.debouncedFunctions.set(name, timeoutId);
    }

    /**
     * Clean up all tracked resources
     */
    cleanup() {
        // Clear all tracked timers
        MemoryManager.timers.forEach(id => clearTimeout(id));
        MemoryManager.timers.clear();

        // Clear all tracked intervals
        MemoryManager.intervals.forEach(id => clearInterval(id));
        MemoryManager.intervals.clear();

        // Remove all tracked event listeners
        MemoryManager.eventListeners.forEach(listeners => {
            listeners.forEach(({ element, event, handler }) => {
                if (element && element.removeEventListener) {
                    element.removeEventListener(event, handler);
                }
            });
        });
        MemoryManager.eventListeners.clear();

        // Clear all debounced function timeouts
        MemoryManager.debouncedFunctions.forEach(id => clearTimeout(id));
        MemoryManager.debouncedFunctions.clear();
    }

    /**
     * Remove a specific timer from tracking
     * @param {number} timerId - Timer ID to remove
     */
    static removeTimer(timerId) {
        this.timers.delete(timerId);
    }

    /**
     * Remove a specific interval from tracking
     * @param {number} intervalId - Interval ID to remove
     */
    static removeInterval(intervalId) {
        this.intervals.delete(intervalId);
    }

    /**
     * Get the count of tracked resources
     * @returns {Object} Resource counts
     */
    static getResourceCounts() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            eventListeners: Array.from(this.eventListeners.values()).reduce((total, arr) => total + arr.length, 0),
            debouncedFunctions: this.debouncedFunctions.size
        };
    }
}
