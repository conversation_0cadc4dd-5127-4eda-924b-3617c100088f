/**
 * DOM Elements Module
 * Centralizes all DOM element queries for better organization and maintainability
 */

// Cache DOM elements once they're found
let domCache = {};

export const DOM = {
    // Tab Elements
    get tabButtons() {
        return domCache.tabButtons || (domCache.tabButtons = document.querySelectorAll('.tab-button'));
    },

    get tabContents() {
        return domCache.tabContents || (domCache.tabContents = document.querySelectorAll('.tab-content'));
    },

    // Provider Selection
    get providerSelect() {
        return domCache.providerSelect || (domCache.providerSelect = document.getElementById('provider-select'));
    },

    // ElevenLabs Elements
    get elevenLabsSettings() {
        return domCache.elevenLabsSettings || (domCache.elevenLabsSettings = document.getElementById('elevenlabs-settings'));
    },

    get apiKeyInput() {
        return domCache.apiKeyInput || (domCache.apiKeyInput = document.getElementById('api-key'));
    },

    get elevenLabsVoiceSettings() {
        return domCache.elevenLabsVoiceSettings || (domCache.elevenLabsVoiceSettings = document.getElementById('elevenlabs-voice-settings'));
    },

    get elevenLabsCreditsDiv() {
        return domCache.elevenLabsCreditsDiv || (domCache.elevenLabsCreditsDiv = document.getElementById('elevenlabs-credits'));
    },

    get elevenLabsVoiceStatus() {
        return domCache.elevenLabsVoiceStatus || (domCache.elevenLabsVoiceStatus = document.getElementById('elevenlabs-voice-status'));
    },

    // Hume AI Elements
    get humeSettings() {
        return domCache.humeSettings || (domCache.humeSettings = document.getElementById('hume-settings'));
    },

    get humeApiKeyInput() {
        return domCache.humeApiKeyInput || (domCache.humeApiKeyInput = document.getElementById('hume-api-key'));
    },

    get humeVoiceSettings() {
        return domCache.humeVoiceSettings || (domCache.humeVoiceSettings = document.getElementById('hume-voice-settings'));
    },

    get humeVoiceSelect() {
        return domCache.humeVoiceSelect || (domCache.humeVoiceSelect = document.getElementById('hume-voice'));
    },

    get humeSpeedSlider() {
        return domCache.humeSpeedSlider || (domCache.humeSpeedSlider = document.getElementById('hume-speed-slider'));
    },

    get humeSpeedValue() {
        return domCache.humeSpeedValue || (domCache.humeSpeedValue = document.getElementById('hume-speed-value'));
    },

    get humeVoiceStatus() {
        return domCache.humeVoiceStatus || (domCache.humeVoiceStatus = document.getElementById('hume-voice-status'));
    },

    // Straico Elements
    get straicoSettings() {
        return domCache.straicoSettings || (domCache.straicoSettings = document.getElementById('straico-settings'));
    },

    get straicoApiKeyInput() {
        return domCache.straicoApiKeyInput || (domCache.straicoApiKeyInput = document.getElementById('straico-api-key'));
    },

    get straicoVoiceSettings() {
        return domCache.straicoVoiceSettings || (domCache.straicoVoiceSettings = document.getElementById('straico-voice-settings'));
    },

    get straicoModelSelect() {
        return domCache.straicoModelSelect || (domCache.straicoModelSelect = document.getElementById('straico-model-select'));
    },

    get straicoVoiceSelect() {
        return domCache.straicoVoiceSelect || (domCache.straicoVoiceSelect = document.getElementById('straico-voice'));
    },

    get straicoSpeedSlider() {
        return domCache.straicoSpeedSlider || (domCache.straicoSpeedSlider = document.getElementById('straico-speed-slider'));
    },

    get straicoSpeedValue() {
        return domCache.straicoSpeedValue || (domCache.straicoSpeedValue = document.getElementById('straico-speed-value'));
    },

    get straicoCreditsDiv() {
        return domCache.straicoCreditsDiv || (domCache.straicoCreditsDiv = document.getElementById('straico-credits'));
    },

    get straicoVoiceStatus() {
        return domCache.straicoVoiceStatus || (domCache.straicoVoiceStatus = document.getElementById('straico-voice-status'));
    },

    // Google Elements
    get googleSettings() {
        return domCache.googleSettings || (domCache.googleSettings = document.getElementById('google-settings'));
    },

    get googleApiKeyInput() {
        return domCache.googleApiKeyInput || (domCache.googleApiKeyInput = document.getElementById('google-api-key'));
    },

    get googleRequestsContainer() {
        return domCache.googleRequestsContainer || (domCache.googleRequestsContainer = document.getElementById('google-requests-container'));
    },

    get googleVoiceSelect() {
        return domCache.googleVoiceSelect || (domCache.googleVoiceSelect = document.getElementById('google-voice'));
    },

    // General Settings
    get showFloatingButtonToggle() {
        return domCache.showFloatingButtonToggle || (domCache.showFloatingButtonToggle = document.getElementById('show-floating-button-toggle'));
    },

    get enableHighlightingToggle() {
        return domCache.enableHighlightingToggle || (domCache.enableHighlightingToggle = document.getElementById('enable-highlighting-toggle'));
    },

    get themeToggle() {
        return domCache.themeToggle || (domCache.themeToggle = document.getElementById('theme-toggle'));
    },

    get disableOnSiteCheckbox() {
        return domCache.disableOnSiteCheckbox || (domCache.disableOnSiteCheckbox = document.getElementById('disable-on-site'));
    },

    get disableOnSiteLabel() {
        return domCache.disableOnSiteLabel || (domCache.disableOnSiteLabel = document.getElementById('disable-on-site-label'));
    },

    get generalSettingsWrapper() {
        return domCache.generalSettingsWrapper || (domCache.generalSettingsWrapper = document.getElementById('general-settings-wrapper'));
    },

    // Voice Settings
    get voiceSelect() {
        return domCache.voiceSelect || (domCache.voiceSelect = document.getElementById('voice'));
    },

    get freeFilterToggle() {
        return domCache.freeFilterToggle || (domCache.freeFilterToggle = document.getElementById('voice-filter-free-toggle'));
    },

    get languageFilterSelect() {
        return domCache.languageFilterSelect || (domCache.languageFilterSelect = document.getElementById('language-filter'));
    },

    get genderFilterSelect() {
        return domCache.genderFilterSelect || (domCache.genderFilterSelect = document.getElementById('gender-filter'));
    },

    // Sliders
    get speedSlider() {
        return domCache.speedSlider || (domCache.speedSlider = document.getElementById('speed-slider'));
    },

    get stabilitySlider() {
        return domCache.stabilitySlider || (domCache.stabilitySlider = document.getElementById('stability-slider'));
    },

    get similaritySlider() {
        return domCache.similaritySlider || (domCache.similaritySlider = document.getElementById('similarity-slider'));
    },

    get styleSlider() {
        return domCache.styleSlider || (domCache.styleSlider = document.getElementById('style-slider'));
    },

    // Slider Values
    get speedValue() {
        return domCache.speedValue || (domCache.speedValue = document.getElementById('speed-value'));
    },

    get stabilityValue() {
        return domCache.stabilityValue || (domCache.stabilityValue = document.getElementById('stability-value'));
    },

    get similarityValue() {
        return domCache.similarityValue || (domCache.similarityValue = document.getElementById('similarity-value'));
    },

    get styleValue() {
        return domCache.styleValue || (domCache.styleValue = document.getElementById('style-value'));
    },

    // Text to Audio Elements
    get textToAudioInput() {
        return domCache.textToAudioInput || (domCache.textToAudioInput = document.getElementById('text-to-audio-input'));
    },

    get convertBtn() {
        return domCache.convertBtn || (domCache.convertBtn = document.getElementById('convert-btn'));
    },

    get downloadContainer() {
        return domCache.downloadContainer || (domCache.downloadContainer = document.getElementById('download-container'));
    },

    get outputFormatContainer() {
        return domCache.outputFormatContainer || (domCache.outputFormatContainer = document.getElementById('output-format-container'));
    },

    get outputFormatSelect() {
        return domCache.outputFormatSelect || (domCache.outputFormatSelect = document.getElementById('output-format'));
    },

    get ttaProviderSpan() {
        return domCache.ttaProviderSpan || (domCache.ttaProviderSpan = document.getElementById('tta-provider'));
    },

    get ttaModelSpan() {
        return domCache.ttaModelSpan || (domCache.ttaModelSpan = document.getElementById('tta-model'));
    },

    get ttaCreditsSpan() {
        return domCache.ttaCreditsSpan || (domCache.ttaCreditsSpan = document.getElementById('tta-credits'));
    },

    get ttaVoiceSpan() {
        return domCache.ttaVoiceSpan || (domCache.ttaVoiceSpan = document.getElementById('tta-voice'));
    },

    get textStatsDiv() {
        return domCache.textStatsDiv || (domCache.textStatsDiv = document.getElementById('text-stats'));
    },

    get clearTextBtn() {
        return domCache.clearTextBtn || (domCache.clearTextBtn = document.getElementById('clear-text-btn'));
    }
};

/**
 * Clear the DOM cache - useful for testing or when DOM structure changes
 */
export function clearDOMCache() {
    domCache = {};
}