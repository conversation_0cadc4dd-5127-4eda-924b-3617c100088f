/**
 * Text-to-Audio Module
 * Handles text-to-audio conversion functionality, text statistics, and download management
 */

import { StorageManager } from './storage-manager.js';

export class TextToAudio {
    constructor(domElements) {
        this.dom = domElements;
        this.updateTtaInfoCallback = null;
    }

    /**
     * Initialize text-to-audio module with event listeners
     */
    initialize() {
        // Text input handler with auto-save
        this.dom.textToAudioInput.addEventListener('input', () => {
            this.handleTextInput();
        });

        // Clear text button
        this.dom.clearTextBtn.addEventListener('click', () => {
            this.clearText();
        });

        // Convert button
        this.dom.convertBtn.addEventListener('click', () => {
            this.handleConvert();
        });

        // Listen for tab switches to update info
        document.addEventListener('tab-switched', (event) => {
            if (event.detail.tabId === 'text-to-audio') {
                this.updateInfo();
            }
        });

        // Listen for runtime messages
        chrome.runtime.onMessage.addListener((request) => {
            this.handleRuntimeMessage(request);
        });
    }

    /**
     * Set callback function for updating TTA info
     * @param {Function} callback - Callback to update TTA info display
     */
    setUpdateTtaInfoCallback(callback) {
        this.updateTtaInfoCallback = callback;
    }

    /**
     * Handle text input events
     */
    async handleTextInput() {
        const text = this.dom.textToAudioInput.value;

        // Auto-save text to local storage
        await StorageManager.saveLocal({ ttaText: text });

        // Update clear button visibility
        this.dom.clearTextBtn.style.display = text.length > 0 ? 'block' : 'none';

        // Update text statistics
        this.updateTextStats(text);
    }

    /**
     * Update text statistics display
     * @param {string} text - Text to analyze
     */
    updateTextStats(text) {
        const charCount = text.length;
        const words = text.trim().split(/\s+/).filter(Boolean);
        const wordCount = words.length;
        const readingTimeSeconds = Math.round((wordCount / 200) * 60);
        const minutes = Math.floor(readingTimeSeconds / 60);
        const seconds = readingTimeSeconds % 60;

        let readingTimeFormatted;
        if (minutes > 0 && seconds > 0) {
            readingTimeFormatted = `${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            readingTimeFormatted = `${minutes}m`;
        } else {
            readingTimeFormatted = `${seconds}s`;
        }

        const spans = this.dom.textStatsDiv.getElementsByTagName('span');
        spans[0].textContent = `Chars: ${charCount}`;
        spans[1].textContent = `Words: ${wordCount}`;
        spans[2].textContent = `Time: ${readingTimeFormatted}`;
    }

    /**
     * Clear text input and reset UI
     */
    clearText() {
        this.dom.textToAudioInput.value = '';
        this.dom.textToAudioInput.dispatchEvent(new Event('input'));
    }

    /**
     * Handle convert button click
     */
    async handleConvert() {
        const text = this.dom.textToAudioInput.value.trim();
        const { sync: syncData } = await StorageManager.loadAll();
        const provider = syncData.selectedProvider || 'google';
        const format = provider === 'google' ? 'wav' : (provider === 'hume' ? 'mp3' : 'mp3');

        if (!text) {
            this.updateStatus('Please enter some text.', 'red');
            return;
        }

        // Confirm for large texts
        if (text.length > 3000) {
            const confirmation = confirm(`The text is over 3000 characters and may use a significant amount of your credits. Do you want to continue?`);
            if (!confirmation) return;
        }

        // Update UI to show conversion in progress
        this.dom.convertBtn.disabled = true;
        this.dom.convertBtn.innerHTML = '<span class="loading-spinner"></span>Converting...';

        // Send conversion request to background script
        chrome.runtime.sendMessage({
            type: 'convert-text-to-audio',
            text: text,
            format: format
        });
    }

    /**
     * Handle runtime messages from background script
     * @param {Object} request - Message from background script
     */
    handleRuntimeMessage(request) {
        switch (request.type) {
            case 'conversion-complete':
                this.updateStatus('Conversion complete! Download started.', 'green');
                this.clearText();
                this.resetConvertButton();
                this.dom.downloadContainer.innerHTML = '';
                break;

            case 'conversion-error':
                this.updateStatus(`Error: ${request.error}`, 'red');
                this.resetConvertButton();
                break;

            case 'conversion-status':
                this.dom.convertBtn.innerHTML = `<span class="loading-spinner"></span>${request.status}`;
                break;

            case 'get-clipboard-text':
                this.handleClipboardRequest();
                break;
        }
    }

    /**
     * Handle clipboard text request
     */
    handleClipboardRequest() {
        const textarea = document.getElementById('clipboard-helper');
        textarea.focus();
        document.execCommand('paste');
        const text = textarea.value;
        chrome.runtime.sendMessage({
            type: 'clipboard-text-response',
            text: text
        });
    }

    /**
     * Reset convert button to default state
     */
    resetConvertButton() {
        this.dom.convertBtn.disabled = false;
        this.dom.convertBtn.innerHTML = 'Convert and Download';
    }

    /**
     * Update status message
     * @param {string} message - Status message
     * @param {string} color - Color for the message
     */
    updateStatus(message, color) {
        console.log(`Status (${color}): ${message}`);
        // Status display would be handled by the main popup controller
    }

    /**
     * Update TTA info display
     */
    updateInfo() {
        if (this.updateTtaInfoCallback) {
            this.updateTtaInfoCallback();
        }
    }

    /**
     * Restore saved text from storage
     * @param {Object} localData - Local storage data
     */
    restoreSettings(localData) {
        if (localData.ttaText) {
            this.dom.textToAudioInput.value = localData.ttaText;
            this.dom.textToAudioInput.dispatchEvent(new Event('input')); // Trigger stats update
        }
    }

    /**
     * Show output format container for specific providers
     */
    showOutputFormat() {
        this.dom.outputFormatContainer.style.display = 'block';
    }

    /**
     * Hide output format container
     */
    hideOutputFormat() {
        this.dom.outputFormatContainer.style.display = 'none';
    }
}