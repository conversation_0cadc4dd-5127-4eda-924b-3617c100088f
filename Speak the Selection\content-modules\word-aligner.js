/**
 * Word Aligner Module
 * Handles word timing estimation, syllable counting, and pause weight calculation for free highlighting
 */

export class FreeWordAligner {
    constructor(text, audioDuration) {
        this.text = text;
        this.audioDuration = audioDuration;
        this.words = text.trim().split(/\s+/);
    }

    /**
     * Get word alignment with timing estimates
     */
    getAlignment() {
        const wordsWithTiming = this.words.map(word => {
            const cleanWord = word.replace(/[^a-zA-Z']/g, '');
            return {
                word: word,
                syllables: this.countSyllables(cleanWord),
                pauseAfter: this.getPauseWeight(word)
            };
        });

        const totalUnits = wordsWithTiming.reduce((sum, w) => sum + w.syllables + w.pauseAfter, 0);
        const timePerUnit = this.audioDuration / totalUnits;
        let currentTime = 0;
        const alignment = [];

        wordsWithTiming.forEach(w => {
            const wordDuration = w.syllables * timePerUnit;
            const pauseDuration = w.pauseAfter * timePerUnit;
            alignment.push({
                word: w.word,
                start: currentTime,
                end: currentTime + wordDuration
            });
            currentTime += wordDuration + pauseDuration;
        });

        return alignment;
    }

    /**
     * Count syllables in a word using vowel-based estimation
     */
    countSyllables(word) {
        if (!word) return 1;

        word = word.toLowerCase();
        let count = 0;
        let previousWasVowel = false;

        // Count vowel groups as syllables
        for (let i = 0; i < word.length; i++) {
            const isVowel = 'aeiouy'.includes(word[i]);
            if (isVowel && !previousWasVowel) count++;
            previousWasVowel = isVowel;
        }

        // Apply syllable counting rules
        if (word.endsWith('e') && count > 1) count--;
        if (word.endsWith('les') && count > 1) count++;
        if (word.endsWith('ed') && !['ted', 'ded'].includes(word.slice(-3))) count--;

        // Ensure minimum syllable count
        if (count === 0) count = 1;

        // Apply length-based adjustments
        if (word.length <= 3) return 1;
        if (word.length >= 12) return Math.max(4, count);

        return count;
    }

    /**
     * Calculate pause weight based on punctuation
     */
    getPauseWeight(word) {
        if (word.match(/[.!?]$/)) return 2.0;  // Strong sentence endings
        if (word.match(/[;:]$/)) return 1.5;   // Semi-colons and colons
        if (word.match(/[,]$/)) return 0.8;    // Commas
        if (word.match(/[-—]$/)) return 0.5;   // Dashes
        return 0; // No pause
    }

    /**
     * Get word count
     */
    getWordCount() {
        return this.words.length;
    }

    /**
     * Get total estimated duration
     */
    getTotalDuration() {
        return this.audioDuration;
    }

    /**
     * Get average time per word
     */
    getAverageTimePerWord() {
        return this.audioDuration / this.words.length;
    }

    /**
     * Get words array
     */
    getWords() {
        return this.words;
    }

    /**
     * Get text
     */
    getText() {
        return this.text;
    }
}