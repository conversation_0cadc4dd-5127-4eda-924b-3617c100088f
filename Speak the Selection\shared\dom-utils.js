// DOM Cache and Optimization Utilities
// Provides caching for DOM queries and optimized DOM operations
// Maintains 100% backward compatibility

class DOMCache {
    static cache = new Map();
    static observers = new Set();
    static fragmentPool = [];

    // Cached querySelector with automatic cleanup
    static get(selector, context = document) {
        const contextId = context.tagName || context.constructor.name || 'document';
        const key = `${contextId}-${selector}`;

        if (this.cache.has(key)) {
            const cached = this.cache.get(key);
            // Verify element is still in DOM
            if (cached && cached.isConnected) {
                return cached;
            } else {
                this.cache.delete(key);
            }
        }

        const element = context.querySelector(selector);
        if (element) {
            this.cache.set(key, element);
        }

        return element;
    }

    // Cached querySelectorAll
    static getAll(selector, context = document) {
        const contextId = context.tagName || context.constructor.name || 'document';
        const key = `all-${contextId}-${selector}`;

        if (this.cache.has(key)) {
            const cached = this.cache.get(key);
            // Quick validation - check if first element is still connected
            if (cached && cached.length > 0 && cached[0].isConnected) {
                return cached;
            } else {
                this.cache.delete(key);
            }
        }

        const elements = context.querySelectorAll(selector);
        if (elements.length > 0) {
            this.cache.set(key, elements);
        }

        return elements;
    }

    // Optimized element creation with fragment pooling
    static createElement(tag, options = {}) {
        const element = document.createElement(tag);

        if (options.className) {
            element.className = options.className;
        }

        if (options.textContent) {
            element.textContent = options.textContent;
        }

        if (options.innerHTML) {
            element.innerHTML = options.innerHTML;
        }

        if (options.attributes) {
            for (const [name, value] of Object.entries(options.attributes)) {
                element.setAttribute(name, value);
            }
        }

        if (options.styles) {
            Object.assign(element.style, options.styles);
        }

        return element;
    }

    // Get or create document fragment for batch DOM operations
    static getFragment() {
        return this.fragmentPool.pop() || document.createDocumentFragment();
    }

    // Return fragment to pool for reuse
    static returnFragment(fragment) {
        // Clear fragment and return to pool
        while (fragment.firstChild) {
            fragment.removeChild(fragment.firstChild);
        }

        if (this.fragmentPool.length < 5) {
            this.fragmentPool.push(fragment);
        }
    }

    // Batch DOM operations using fragment
    static batchAppend(container, elements) {
        const fragment = this.getFragment();

        elements.forEach(element => {
            if (element) {
                fragment.appendChild(element);
            }
        });

        container.appendChild(fragment);
        this.returnFragment(fragment);
    }

    // Optimized highlighting with reduced reflow
    static batchHighlight(elements, className) {
        // Batch DOM modifications to reduce reflow
        const fragment = this.getFragment();
        const placeholder = document.createComment('highlight-placeholder');

        elements.forEach(element => {
            if (element && element.parentNode) {
                // Create wrapper
                const wrapper = this.createElement('span', {
                    className: className
                });

                // Use fragment to minimize DOM manipulation
                element.parentNode.insertBefore(placeholder, element);
                wrapper.appendChild(element);
                placeholder.parentNode.replaceChild(wrapper, placeholder);
            }
        });

        this.returnFragment(fragment);
    }

    // Set up mutation observer for cache invalidation
    static observeChanges(container, callback) {
        const observer = new MutationObserver((mutations) => {
            let shouldInvalidate = false;

            mutations.forEach(mutation => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    shouldInvalidate = true;
                }
            });

            if (shouldInvalidate) {
                this.invalidateCache(container);
                if (callback) callback(mutations);
            }
        });

        observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'id']
        });

        this.observers.add(observer);
        return observer;
    }

    // Invalidate cache for specific container
    static invalidateCache(container) {
        const contextId = container.tagName || container.constructor.name;

        for (const key of this.cache.keys()) {
            if (key.startsWith(contextId)) {
                this.cache.delete(key);
            }
        }
    }

    // Clear all cached elements
    static clear() {
        this.cache.clear();

        // Disconnect all observers
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();

        // Clear fragment pool
        this.fragmentPool.length = 0;
    }

    // Get cache statistics
    static getStats() {
        return {
            cacheSize: this.cache.size,
            observerCount: this.observers.size,
            fragmentPoolSize: this.fragmentPool.length
        };
    }
}

// Viewport utilities for better positioning
class ViewportUtils {
    static getViewportInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
    }

    static isElementInViewport(element, threshold = 0) {
        const rect = element.getBoundingClientRect();
        const viewport = this.getViewportInfo();

        return (
            rect.top >= threshold &&
            rect.left >= threshold &&
            rect.bottom <= viewport.height - threshold &&
            rect.right <= viewport.width - threshold
        );
    }

    static getOptimalPosition(selectionRect, elementSize, offset = { x: 10, y: 10 }) {
        const viewport = this.getViewportInfo();
        const margin = 20;

        // Calculate preferred position (right of selection)
        let x = selectionRect.right + offset.x;
        let y = selectionRect.top + offset.y;

        // Adjust if outside viewport
        if (x + elementSize.width > viewport.width - margin) {
            x = selectionRect.left - elementSize.width - offset.x;
        }

        if (y + elementSize.height > viewport.height - margin) {
            y = selectionRect.bottom - elementSize.height - offset.y;
        }

        // Ensure minimum margins
        x = Math.max(margin, Math.min(x, viewport.width - elementSize.width - margin));
        y = Math.max(margin, Math.min(y, viewport.height - elementSize.height - margin));

        return { x, y };
    }
}

export { DOMCache, ViewportUtils };