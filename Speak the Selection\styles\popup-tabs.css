/* Tab Navigation */
.tab-nav {
    display: flex;
    gap: 4px;
    padding: 4px;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 12px;
    margin-bottom: 10px;
    box-shadow: var(--shadow);
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    cursor: pointer;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    color: var(--muted);
    border-radius: 8px;
    transition: all .2s ease;
    border-bottom: 0 !important;
    outline: 0;
    position: relative;
}

.tab-button:hover {
    background: var(--surface-2);
    color: var(--text);
    transform: translateY(-1px);
}

.tab-button.active {
    background: linear-gradient(135deg, var(--primary), var(--primary-700));
    color: #ffffff;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.tab-button.active:hover {
    background: linear-gradient(135deg, var(--primary-500), var(--primary));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
    .tab-button.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
        color: #ffffff;
        box-shadow: 0 2px 8px rgba(96, 165, 250, 0.4), 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .tab-button.active:hover {
        background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
        box-shadow: 0 4px 12px rgba(96, 165, 250, 0.5), 0 2px 4px rgba(0, 0, 0, 0.3);
    }
}

.tab-button:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
}