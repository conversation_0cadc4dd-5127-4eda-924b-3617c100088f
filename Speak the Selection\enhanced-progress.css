/* Enhanced Progress Bar for Google TTS */
.tts-enhanced-progress {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 2147483647 !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 16px 20px !important;
    border-radius: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 13px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-width: 280px !important;
    transition: all 0.3s ease !important;
}

.tts-enhanced-progress.light-theme {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.tts-progress-header {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    gap: 8px !important;
}

.tts-progress-icon {
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #FF9800 !important;
    border-top-color: transparent !important;
    border-radius: 50% !important;
    animation: tts-enhanced-spin 1s linear infinite !important;
}

.tts-progress-title {
    font-weight: 600 !important;
    color: #FF9800 !important;
    margin: 0 !important;
}

.tts-progress-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.tts-progress-bar-container {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    height: 8px !important;
    overflow: hidden !important;
}

.tts-enhanced-progress.light-theme .tts-progress-bar-container {
    background: rgba(0, 0, 0, 0.1) !important;
}

.tts-progress-bar-fill {
    height: 100% !important;
    background: linear-gradient(90deg, #FF9800, #FFC107) !important;
    border-radius: 8px !important;
    transition: width 0.3s ease !important;
    width: 0% !important;
}

.tts-progress-info {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-size: 12px !important;
    opacity: 0.8 !important;
}

.tts-progress-chunks {
    font-weight: 500 !important;
}

.tts-progress-time {
    font-weight: 500 !important;
}

.tts-progress-status {
    font-size: 11px !important;
    opacity: 0.7 !important;
    margin-top: 4px !important;
}

.tts-progress-close {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: none !important;
    border: none !important;
    color: inherit !important;
    cursor: pointer !important;
    font-size: 16px !important;
    opacity: 0.6 !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 4px !important;
}

.tts-progress-close:hover {
    opacity: 1 !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

.tts-enhanced-progress.light-theme .tts-progress-close:hover {
    background: rgba(0, 0, 0, 0.1) !important;
}

@keyframes tts-enhanced-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Pulsing animation for long operations */
.tts-enhanced-progress.long-operation {
    animation: tts-progress-pulse 2s ease-in-out infinite !important;
}

@keyframes tts-progress-pulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    50% {
        transform: scale(1.02);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .tts-enhanced-progress {
        right: 10px !important;
        top: 10px !important;
        min-width: 250px !important;
        padding: 12px 16px !important;
    }
}

/* Heartbeat mode for long single chunks */
.tts-enhanced-progress.heartbeat-mode {
    animation: tts-heartbeat-pulse 3s ease-in-out infinite !important;
}

.tts-enhanced-progress.heartbeat-mode .tts-progress-bar-fill {
    background: linear-gradient(90deg, #FF9800, #FFC107, #FF9800) !important;
    background-size: 200% 100% !important;
    animation: tts-heartbeat-flow 4s ease-in-out infinite !important;
}

.tts-enhanced-progress.heartbeat-mode .tts-progress-icon {
    border-color: #FF9800 !important;
    animation: tts-heartbeat-spin 2s linear infinite !important;
}

@keyframes tts-heartbeat-pulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(255, 152, 0, 0.3);
    }

    50% {
        transform: scale(1.02);
        box-shadow: 0 12px 40px rgba(255, 152, 0, 0.5);
    }
}

@keyframes tts-heartbeat-flow {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes tts-heartbeat-spin {
    0% {
        transform: rotate(0deg);
        border-top-color: #FF9800;
    }

    25% {
        border-top-color: #FFC107;
    }

    50% {
        transform: rotate(180deg);
        border-top-color: #FF9800;
    }

    75% {
        border-top-color: #FFC107;
    }

    100% {
        transform: rotate(360deg);
        border-top-color: #FF9800;
    }
}

/* Complexity-specific styling */
.tts-enhanced-progress.complexity-simple .tts-progress-title {
    color: #4CAF50 !important;
}

.tts-enhanced-progress.complexity-moderate .tts-progress-title {
    color: #FF9800 !important;
}

.tts-enhanced-progress.complexity-complex .tts-progress-title {
    color: #F44336 !important;
}

.tts-enhanced-progress.complexity-complex .tts-progress-bar-fill {
    background: linear-gradient(90deg, #F44336, #FF9800, #F44336) !important;
}

.tts-enhanced-progress.complexity-complex .tts-progress-icon {
    border-color: #F44336 !important;
    animation: tts-heartbeat-spin 1.5s linear infinite !important;
}