/* Theme overrides for user preference */
.theme-light {
    --bg: #f7f8fb !important;
    --surface: #ffffff !important;
    --surface-2: #f2f5ff !important;
    --text: #111827 !important;
    --muted: #6b7280 !important;
    --border: #e5e7eb !important;

    --primary: #2563eb !important;
    --primary-500: #3b82f6 !important;
    --primary-50: #eff6ff !important;
    --primary-700: #1d4ed8 !important;
    --ring: rgba(37, 99, 235, .35) !important;

    --success: #16a34a !important;
    --warning: #d97706 !important;
    --danger: #dc2626 !important;

    --shadow: 0 8px 24px rgba(16, 24, 40, 0.08) !important;
    --inset: inset 0 1px 0 rgba(255, 255, 255, .6) !important;
}

.theme-dark {
    --bg: #0b1220 !important;
    --surface: #0f172a !important;
    --surface-2: #0b132e !important;
    --text: #e5e7eb !important;
    --muted: #94a3b8 !important;
    --border: #1f2a44 !important;

    --primary: #60a5fa !important;
    --primary-500: #60a5fa !important;
    --primary-50: #0b1220 !important;
    --primary-700: #3b82f6 !important;
    --ring: rgba(96, 165, 250, .5) !important;

    --success: #16a34a !important;
    --warning: #d97706 !important;
    --danger: #dc2626 !important;

    --shadow: 0 8px 24px rgba(0, 0, 0, 0.35) !important;
    --inset: inset 0 1px 0 rgba(255, 255, 255, .05) !important;
}

/* Theme-specific overrides for tab visibility */
.theme-light .tab-button:hover {
    background: #f2f5ff !important;
    color: #111827 !important;
}

.theme-dark .tab-button:hover {
    background: #0b132e !important;
    color: #e5e7eb !important;
}

@media (prefers-color-scheme: dark) {

    /* Add borders and hover effects to all interactive elements for better visibility */
    .setting-group select,
    .setting-group button,
    .filter-group,
    .provider-settings>button {
        border: 1px solid var(--border);
        transition: border-color .2s ease, box-shadow .2s ease;
    }

    .setting-group select:hover,
    .setting-group button:hover,
    .filter-group:hover,
    .provider-settings>button:hover {
        border-color: var(--primary-700);
        box-shadow: 0 0 8px var(--ring);
    }
}

/* Tooltip styling for better visibility */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: var(--text);
    color: var(--surface);
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    pointer-events: none;
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--text);
    z-index: 1000;
    pointer-events: none;
}

@media (prefers-color-scheme: dark) {
    [title]:hover::after {
        background: var(--surface);
        color: var(--text);
        border: 1px solid var(--border);
    }

    [title]:hover::before {
        border-top-color: var(--surface);
    }
}

/* Theme-specific tooltip overrides */
.theme-light [title]:hover::after {
    background: #111827 !important;
    color: #ffffff !important;
    border: none !important;
}

.theme-light [title]:hover::before {
    border-top-color: #111827 !important;
}

.theme-dark [title]:hover::after {
    background: #ffffff !important;
    color: #111827 !important;
    border: 1px solid #374151 !important;
}

.theme-dark [title]:hover::before {
    border-top-color: #ffffff !important;
}

/* Theme Toggle Button */
.theme-toggle-container {
    display: flex;
    justify-content: flex-start;
}

.theme-toggle-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--surface-2);
    border: 1px solid var(--border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text);
    font-size: 13px;
    font-weight: 500;
    width: auto;
    min-width: 100px;
}

.theme-toggle-btn:hover {
    background: var(--primary-50);
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

.theme-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    position: relative;
}

.theme-svg {
    fill: currentColor;
    transition: all 0.3s ease;
}

.theme-svg path {
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Show appropriate icon based on theme */
.theme-toggle-btn[data-theme="auto"] .auto-icon {
    opacity: 1;
}

.theme-toggle-btn[data-theme="light"] .light-icon {
    opacity: 1;
}

.theme-toggle-btn[data-theme="dark"] .dark-icon {
    opacity: 1;
}

/* Theme-specific colors */
.theme-toggle-btn[data-theme="auto"] {
    background: linear-gradient(135deg, var(--primary-50), var(--surface-2));
    border-color: var(--primary);
}

.theme-toggle-btn[data-theme="light"] {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    border-color: #f59e0b;
    color: #92400e;
}

.theme-toggle-btn[data-theme="dark"] {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-color: #64748b;
    color: #cbd5e1;
}

.theme-label {
    font-weight: 500;
    text-transform: capitalize;
}