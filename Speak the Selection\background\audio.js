let offscreenPort = null;

export function setOffscreenPort(port) {
    offscreenPort = port;
    offscreenPort.onDisconnect.addListener(() => {
        offscreenPort = null;
    });
}

export function getOffscreenPort() {
    return offscreenPort;
}
export async function setupOffscreenDocument() {
    if (await chrome.offscreen.hasDocument()) {
        return;
    }
    await chrome.offscreen.createDocument({
        url: 'offscreen.html',
        reasons: ['AUDIO_PLAYBACK'],
        justification: 'To play audio from the TTS API'
    });
}


async function checkUsage(duration) {
    const { usage, lastReset } = await chrome.storage.local.get(['usage', 'lastReset']);
    const now = new Date();
    const lastResetDate = lastReset ? new Date(lastReset) : new Date(0);

    if (now.getMonth() !== lastResetDate.getMonth() || now.getFullYear() !== lastResetDate.getFullYear()) {
        await chrome.storage.local.set({ usage: 0, lastReset: now.getTime() });
        return { canPlay: true, remaining: 3600 };
    }

    const newUsage = (usage || 0) + duration;
    if (newUsage > 3600) {
        return { canPlay: false, remaining: 3600 - usage };
    }

    await chrome.storage.local.set({ usage: newUsage });
    return { canPlay: true, remaining: 3600 - newUsage };
}

export async function playAudioQueue(audioQueue, tabId, enableHighlighting, fromClipboard) {
    await setupOffscreenDocument();
    if (!offscreenPort) {
        return;
    }



    const { selectedProvider, playbackSpeed, humePlaybackSpeed, straicoPlaybackSpeed, volume } = await chrome.storage.sync.get(['selectedProvider', 'playbackSpeed', 'humePlaybackSpeed', 'straicoPlaybackSpeed', 'volume']);

    let speed = 1.0;
    if (selectedProvider === 'straico' && straicoPlaybackSpeed) {
        speed = straicoPlaybackSpeed;
    } else if (selectedProvider === 'hume' && humePlaybackSpeed) {
        speed = humePlaybackSpeed;
    } else if (playbackSpeed) {
        speed = playbackSpeed;
    }

    const message = {
        type: 'play-audio-queue',
        queue: audioQueue,
        volume: volume,
        speed: speed,
        tabId: tabId,
        enableHighlighting: enableHighlighting,
        fromClipboard: fromClipboard
    };

    offscreenPort.postMessage(message);
}

export async function stopAudio() {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'stop-audio' });
    }
    // Clear badge when audio is stopped
    chrome.action.setBadgeText({ text: '' });
}

export async function pauseAudio() {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'pause-audio-offscreen' });
    }
}

export async function resumeAudio() {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'resume-audio-offscreen' });
    }
}

export async function setVolume(volume) {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'set-volume-offscreen', volume: volume });
    }
}

export async function setSpeed(speed) {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'set-speed-offscreen', speed: speed });
    }
}

export async function seekAudio(time) {
    await setupOffscreenDocument();
    if (offscreenPort) {
        offscreenPort.postMessage({ type: 'seek-audio-offscreen', time: time });
    }
}

export function getAudioDuration(url) {
    return new Promise(async (resolve, reject) => {
        await setupOffscreenDocument();

        if (!offscreenPort || !offscreenPort.sender) {
            // Wait for the port to be re-established
            setTimeout(() => getAudioDuration(url).then(resolve).catch(reject), 100);
            return;
        }

        const messageListener = (response) => {
            if (response.type === 'audio-duration-response' && response.url === url) {
                offscreenPort.onMessage.removeListener(messageListener);
                if (response.error) {
                    reject(new Error(response.error));
                } else {
                    resolve(response.duration);
                }
            }
        };

        offscreenPort.onMessage.addListener(messageListener);
        try {
            offscreenPort.postMessage({ type: 'get-audio-duration', url: url });
        } catch (error) {
            offscreenPort.onMessage.removeListener(messageListener);
            reject(error);
        }

        // Timeout to prevent the promise from hanging indefinitely
        setTimeout(() => {
            offscreenPort.onMessage.removeListener(messageListener);
            reject(new Error('Audio duration request timed out.'));
        }, 10000); // Increased timeout
    });
}

// Get actual audio player state from offscreen document
export function getActualAudioState() {
    return new Promise(async (resolve, reject) => {
        await setupOffscreenDocument();

        if (!offscreenPort) {
            resolve({ state: 'idle', currentTime: 0, duration: 0, isPlaying: false });
            return;
        }

        const messageListener = (response) => {
            if (response.type === 'audio-state-response') {
                offscreenPort.onMessage.removeListener(messageListener);
                resolve({
                    state: response.state,
                    currentTime: response.currentTime || 0,
                    duration: response.duration || 0,
                    isPlaying: response.isPlaying || false
                });
            }
        };

        offscreenPort.onMessage.addListener(messageListener);
        try {
            offscreenPort.postMessage({ type: 'get-audio-state' });
        } catch (error) {
            offscreenPort.onMessage.removeListener(messageListener);
            resolve({ state: 'idle', currentTime: 0, duration: 0, isPlaying: false });
        }

        // Timeout fallback
        setTimeout(() => {
            offscreenPort.onMessage.removeListener(messageListener);
            resolve({ state: 'idle', currentTime: 0, duration: 0, isPlaying: false });
        }, 1000);
    });
}
