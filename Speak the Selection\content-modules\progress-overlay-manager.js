/**
 * Progress Overlay Manager Module
 * Handles enhanced progress displays and loading overlays for long-running TTS operations
 */

export class ProgressOverlayManager {
    constructor(themeManager) {
        this.themeManager = themeManager;
        this.enhancedProgressOverlay = null;
    }

    /**
     * Show enhanced progress overlay
     */
    showEnhancedProgress(currentRange = null) {
        // Only show for longer operations or Google TTS
        if (this.enhancedProgressOverlay) return; // Already showing

        // Load enhanced progress CSS if not already loaded
        if (!document.getElementById('tts-enhanced-progress-styles')) {
            const link = document.createElement('link');
            link.id = 'tts-enhanced-progress-styles';
            link.rel = 'stylesheet';
            link.href = chrome.runtime.getURL('enhanced-progress.css');
            document.head.appendChild(link);
        }

        this.enhancedProgressOverlay = document.createElement('div');
        this.enhancedProgressOverlay.className = 'tts-enhanced-progress';

        // Apply theme using theme manager
        if (this.themeManager) {
            this.themeManager.applyThemeToProgressOverlay(this.enhancedProgressOverlay, currentRange);
        }

        this.enhancedProgressOverlay.innerHTML = `
            <button class="tts-progress-close" aria-label="Close progress">&times;</button>
            <div class="tts-progress-header">
                <div class="tts-progress-icon"></div>
                <div class="tts-progress-title">Google TTS Processing</div>
            </div>
            <div class="tts-progress-content">
                <div class="tts-progress-bar-container">
                    <div class="tts-progress-bar-fill"></div>
                </div>
                <div class="tts-progress-info">
                    <span class="tts-progress-chunks">Chunk 0 of 0</span>
                    <span class="tts-progress-time">Estimating...</span>
                </div>
                <div class="tts-progress-status">Initializing...</div>
            </div>
        `;

        // Add close functionality
        const closeBtn = this.enhancedProgressOverlay.querySelector('.tts-progress-close');
        closeBtn.addEventListener('click', () => this.hideEnhancedProgress());

        document.body.appendChild(this.enhancedProgressOverlay);

        // Add long operation class if estimated time is > 30 seconds
        setTimeout(() => {
            if (this.enhancedProgressOverlay) {
                this.enhancedProgressOverlay.classList.add('long-operation');
            }
        }, 30000);
    }

    /**
     * Hide enhanced progress overlay
     */
    hideEnhancedProgress() {
        if (this.enhancedProgressOverlay) {
            this.enhancedProgressOverlay.remove();
            this.enhancedProgressOverlay = null;
        }
    }

    /**
     * Handle enhanced progress updates for chunk-based processing
     */
    handleEnhancedProgressUpdate(data) {
        if (!this.enhancedProgressOverlay) {
            this.showEnhancedProgress();
        }

        if (!this.enhancedProgressOverlay) return;

        const progressBar = this.enhancedProgressOverlay.querySelector('.tts-progress-bar-fill');
        const chunksInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-chunks');
        const timeInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-time');
        const statusInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-status');

        if (progressBar) {
            progressBar.style.width = `${data.progress * 100}%`;
        }

        if (chunksInfo) {
            chunksInfo.textContent = `Chunk ${data.currentChunk} of ${data.totalChunks}`;
        }

        if (timeInfo) {
            const remaining = data.estimatedRemaining;
            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60);
                const seconds = remaining % 60;
                if (minutes > 0) {
                    timeInfo.textContent = `~${minutes}m ${seconds}s left`;
                } else {
                    timeInfo.textContent = `~${seconds}s left`;
                }
            } else {
                timeInfo.textContent = 'Almost done...';
            }
        }

        if (statusInfo) {
            statusInfo.textContent = data.status;
        }

        // Auto-hide when complete
        if (data.progress >= 1) {
            setTimeout(() => {
                this.hideEnhancedProgress();
            }, 2000);
        }
    }

    /**
     * Handle heartbeat progress updates for continuous processing
     */
    handleHeartbeatProgressUpdate(data) {
        if (!this.enhancedProgressOverlay) {
            this.showEnhancedProgress();
        }

        if (!this.enhancedProgressOverlay) return;

        // Update the title for heartbeat mode with complexity info
        const titleElement = this.enhancedProgressOverlay.querySelector('.tts-progress-title');
        if (titleElement) {
            const complexityEmoji = {
                'simple': '📝',
                'moderate': '📄',
                'complex': '📚'
            };
            const emoji = complexityEmoji[data.complexity] || '📝';
            titleElement.textContent = `${emoji} Google TTS Processing (Large Text)`;
        }

        // For heartbeat, show animated progress bar based on elapsed time
        const progressBar = this.enhancedProgressOverlay.querySelector('.tts-progress-bar-fill');
        const chunksInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-chunks');
        const timeInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-time');
        const statusInfo = this.enhancedProgressOverlay.querySelector('.tts-progress-status');

        if (progressBar) {
            // Show progress based on elapsed time vs estimated time
            const progressPercent = Math.min(85, 10 + (data.elapsed / Math.max(data.estimatedTime, 1)) * 75);
            progressBar.style.width = `${progressPercent}%`;
            progressBar.style.transition = 'width 2s ease-in-out';
        }

        if (chunksInfo) {
            const complexityText = data.complexity ? ` (${data.complexity} text)` : '';
            chunksInfo.textContent = `Processing ${data.textLength.toLocaleString()} characters${complexityText}`;
        }

        if (timeInfo) {
            const remaining = Math.max(0, data.estimatedTime - data.elapsed);
            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60);
                const seconds = Math.floor(remaining % 60);
                if (minutes > 0) {
                    timeInfo.textContent = `~${minutes}m ${seconds}s remaining`;
                } else {
                    timeInfo.textContent = `~${seconds}s remaining`;
                }
            } else {
                timeInfo.textContent = 'Almost done...';
            }
        }

        if (statusInfo) {
            statusInfo.textContent = data.status;
        }

        // Add heartbeat animation class
        this.enhancedProgressOverlay.classList.add('heartbeat-mode');

        // Add complexity-specific styling
        if (data.complexity) {
            this.enhancedProgressOverlay.classList.remove('complexity-simple', 'complexity-moderate', 'complexity-complex');
            this.enhancedProgressOverlay.classList.add(`complexity-${data.complexity}`);
        }
    }

    /**
     * Check if enhanced progress is currently showing
     */
    isEnhancedProgressVisible() {
        return this.enhancedProgressOverlay !== null;
    }

    /**
     * Format time for display
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Update progress overlay theme when theme changes
     */
    updateTheme(currentRange = null) {
        if (this.enhancedProgressOverlay && this.themeManager) {
            this.themeManager.applyThemeToProgressOverlay(this.enhancedProgressOverlay, currentRange);
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.hideEnhancedProgress();
    }
}