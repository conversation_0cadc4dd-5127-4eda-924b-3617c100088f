# Progress: TTS Chrome Extension

## What Works
- **Multi-provider Support:** The extension supports ElevenLabs, Hume AI, and Straico TTS providers.
- **Text Selection:** Users can select text on a webpage to activate the TTS functionality.
- **Context Menu Integration:** A "Read aloud" option is available in the context menu when text is selected, which also displays the estimated reading time, word count, and character count.
- **Configurable Settings:** Users can configure the extension through a popup, including:
    - TTS provider selection
    - API keys for each provider
    - Voice selection with filtering
    - Playback speed and volume
    - Toggles for the floating play button and word highlighting
- **Background Audio Playback:** Audio is played in the background using an offscreen document to ensure uninterrupted playback.
- **Text Highlighting:** The extension highlights words as they are spoken for all providers. For providers that don't support it natively, a custom syllable-based timing estimation is used.
- **Long Text Handling:** The extension automatically splits long texts into smaller chunks for seamless playback.
- **Credit Usage Warning:** A confirmation dialog is shown for very long text selections to warn the user about high credit usage.
- **Auto-closing Player:** The player automatically closes when playback is finished.
- **Persistent Highlighting:** The word highlighting remains visible even if the user clicks elsewhere on the page.
- **Clipboard Support:** The extension can read text from the clipboard if no text is selected.
- **Floating Player:** The floating player is now fully functional and visible, with correct styling for both minimized and expanded states.

## What's Left to Build
- Continue to address user feedback and improve the extension.
- Plan for any new feature enhancements or performance optimizations.

## Current Status
The extension is functional, but there is a critical bug that makes the floating player's text and controls invisible in the expanded state.

## Known Issues
- **Player Visibility:** The text and controls inside the expanded player are not visible. This is likely due to a CSS conflict that is causing the text to inherit the wrong color from the host page.

## Evolution of Project Decisions
- The clipboard reading mechanism has been refactored multiple times to find a reliable solution that works within the constraints of the Chrome Extension environment. The final implementation uses content script injection to access the clipboard.
- The floating player's visibility issue was a complex problem that required a deep dive into the Shadow DOM's rendering lifecycle and CSS interactions. The final solution involved a combination of fixes to the `shadowHost` initialization, CSS selectors, and the removal of an aggressive CSS reset.
