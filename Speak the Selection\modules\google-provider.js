/**
 * Google Provider Module
 * Handles all Google TTS-specific functionality including voice selection and quota tracking
 */

import { StorageManager } from './storage-manager.js';
import { GOOGLE_GEMINI_VOICES } from '../shared/google-gemini-voices.js';

export class GoogleProvider {
    constructor(domElements) {
        this.dom = domElements;
    }

    /**
     * Initialize Google provider with event listeners
     */
    initialize() {
        // API key auto-save handler
        this.dom.googleApiKeyInput.addEventListener('input',
            StorageManager.createAutoSaveHandler(
                this.dom.googleApiKeyInput,
                'googleApiKey',
                () => {
                    this.updateTtaInfo();
                }
            )
        );

        // Voice selection handler
        this.dom.googleVoiceSelect.addEventListener('change', () => {
            StorageManager.saveSync({ selectedGoogleVoice: this.dom.googleVoiceSelect.value });
            this.updateTtaInfo();
        });
    }

    /**
     * Helper function to get Pacific Time date
     */
    getPacificTimeDate() {
        const now = new Date();
        // Use Intl.DateTimeFormat for more reliable timezone conversion
        const pacificTime = new Intl.DateTimeFormat('en-CA', {
            timeZone: 'America/Los_Angeles',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).format(now);
        return pacificTime; // Returns YYYY-MM-DD format
    }

    /**
     * Helper function to get Google quota status with visual styling
     */
    async getGoogleQuotaInfo() {
        const { local: localData } = await StorageManager.loadAll();
        const today = this.getPacificTimeDate();
        const count = localData.googleRequests && localData.googleRequests[today] ? localData.googleRequests[today] : 0;
        const isOverQuota = count >= 15;

        return {
            count,
            isOverQuota,
            displayText: `Today's Requests: ${count}/15`,
            style: isOverQuota ? 'color: #dc2626; font-weight: bold;' : 'color: var(--muted);'
        };
    }

    /**
     * Display Google request usage information
     */
    async displayUsageInfo() {
        const quotaInfo = await this.getGoogleQuotaInfo();
        this.dom.googleRequestsContainer.textContent = quotaInfo.displayText;
        this.dom.googleRequestsContainer.style.cssText = quotaInfo.style;
    }

    /**
     * Populate Google voice selection dropdown
     */
    populateVoiceSelect(voices, selectedVoiceId) {
        this.dom.googleVoiceSelect.innerHTML = '';

        if (!voices || voices.length === 0) {
            this.dom.googleVoiceSelect.innerHTML = '<option>No voices found</option>';
            return;
        }

        // If no voice is selected, default to "Kore" (matches google-tts.js fallback)
        if (!selectedVoiceId && voices.length > 0) {
            const defaultVoice = voices.find(v => v.name === 'Kore') || voices[0];
            selectedVoiceId = defaultVoice.name;
            // Save the default selection
            StorageManager.saveSync({ selectedGoogleVoice: selectedVoiceId });
        }

        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.name;
            option.textContent = `${voice.name} (${voice.description})`;
            if (voice.name === selectedVoiceId) option.selected = true;
            this.dom.googleVoiceSelect.appendChild(option);
        });
    }

    /**
     * Update Text-to-Audio info display
     */
    async updateTtaInfo() {
        this.dom.ttaCreditsSpan.style.visibility = 'visible';
        const quotaInfo = await this.getGoogleQuotaInfo();
        this.dom.ttaCreditsSpan.textContent = quotaInfo.displayText;
        this.dom.ttaCreditsSpan.style.cssText = quotaInfo.style;

        this.dom.ttaModelSpan.style.visibility = 'visible';
        this.dom.ttaModelSpan.textContent = `Model: gemini-2.5-flash-preview-tts`;

        const selectedVoice = this.dom.googleVoiceSelect.options[this.dom.googleVoiceSelect.selectedIndex]?.text;
        this.dom.ttaVoiceSpan.textContent = `Voice: ${selectedVoice || 'N/A'}`;
    }

    /**
     * Restore settings from storage
     */
    async restoreSettings(syncData, localData) {
        // Restore API key
        if (syncData.googleApiKey) {
            this.dom.googleApiKeyInput.value = syncData.googleApiKey;
        }

        // Populate Google voice select with GOOGLE_GEMINI_VOICES
        this.populateVoiceSelect(GOOGLE_GEMINI_VOICES, syncData.selectedGoogleVoice);

        // Display usage info
        await this.displayUsageInfo();
    }

    /**
     * Show provider-specific UI elements
     */
    show() {
        this.dom.googleSettings.style.display = 'block';
    }

    /**
     * Hide provider-specific UI elements
     */
    hide() {
        this.dom.googleSettings.style.display = 'none';
    }
}