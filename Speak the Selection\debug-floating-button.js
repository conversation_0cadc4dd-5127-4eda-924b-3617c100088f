/**
 * Debug script to test floating button functionality
 * Run this in the browser console to check if the floating button setting is working
 */

console.log('=== Floating Button Debug Test ===');

// Check if the extension is loaded
if (typeof window.TTSExtension !== 'undefined') {
    console.log('✓ TTS Extension is loaded');
    
    // Check if EventHandler is available
    if (window.TTSExtension.eventHandler) {
        console.log('✓ EventHandler is available');
        console.log('Current showFloatingButton setting:', window.TTSExtension.eventHandler.showFloatingButton);
    } else {
        console.log('✗ EventHandler is not available');
    }
    
    // Check storage setting
    chrome.storage.sync.get(['showFloatingButton'], (result) => {
        console.log('Storage showFloatingButton setting:', result.showFloatingButton);
        console.log('Setting should default to true if undefined:', result.showFloatingButton !== false);
    });
    
    // Test text selection
    console.log('\n=== Text Selection Test ===');
    console.log('1. Select some text on this page');
    console.log('2. Check if floating button appears');
    console.log('3. If not appearing, check the console for errors');
    
} else {
    console.log('✗ TTS Extension is not loaded');
    console.log('Make sure the extension is enabled and the page is refreshed');
}

// Helper function to toggle the setting for testing
window.toggleFloatingButton = async function() {
    const current = await chrome.storage.sync.get(['showFloatingButton']);
    const newValue = !current.showFloatingButton;
    await chrome.storage.sync.set({ showFloatingButton: newValue });
    console.log('Floating button setting changed to:', newValue);
};

console.log('\n=== Test Commands ===');
console.log('Run toggleFloatingButton() to toggle the setting');
console.log('Then try selecting text to see if it works');
