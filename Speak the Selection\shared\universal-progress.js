// Universal Progress Tracker for All TTS Providers
// Extends existing Google TTS progress system to support all providers
// Maintains 100% backward compatibility

class UniversalProgressTracker {
    static activeProgress = new Map();
    static providerConfigs = {
        google: {
            showComplexity: true,
            showHeartbeat: true,
            estimateMethod: 'text-based',
            color: '#FF9800'
        },
        elevenlabs: {
            showStreaming: true,
            showCredits: false, // Don't show credits in progress
            estimateMethod: 'streaming',
            color: '#2196F3'
        },
        hume: {
            showEmotion: false, // Keep simple for now
            showStreaming: true,
            estimateMethod: 'streaming',
            color: '#9C27B0'
        },
        straico: {
            showTokens: false, // Keep simple for now
            showStreaming: true,
            estimateMethod: 'streaming',
            color: '#4CAF50'
        }
    };

    static create(provider, options = {}) {
        const config = this.providerConfigs[provider] || this.providerConfigs.google;
        return new ProgressTracker(provider, { ...config, ...options });
    }

    static getProgressMessage(provider, data) {
        const messages = {
            google: this.getGoogleMessage(data),
            elevenlabs: this.getElevenLabsMessage(data),
            hume: this.getHumeMessage(data),
            straico: this.getStraicoMessage(data)
        };

        return messages[provider] || `Processing with ${provider}...`;
    }

    static getGoogleMessage(data) {
        // Keep existing Google messages
        if (data.complexity) {
            return `Processing ${data.complexity} text (${data.textLength?.toLocaleString()} characters)...`;
        }
        return `Processing chunk ${data.currentChunk || 1} of ${data.totalChunks || 1}`;
    }

    static getElevenLabsMessage(data) {
        if (data.currentChunk && data.totalChunks > 1) {
            return `🎵 ElevenLabs streaming chunk ${data.currentChunk} of ${data.totalChunks}`;
        }
        return '🎵 ElevenLabs generating high-quality audio...';
    }

    static getHumeMessage(data) {
        if (data.currentChunk && data.totalChunks > 1) {
            return `😊 Hume AI processing chunk ${data.currentChunk} of ${data.totalChunks}`;
        }
        return '😊 Hume AI creating emotional speech...';
    }

    static getStraicoMessage(data) {
        if (data.currentChunk && data.totalChunks > 1) {
            return `⚡ Straico processing chunk ${data.currentChunk} of ${data.totalChunks}`;
        }
        return '⚡ Straico generating speech...';
    }

    static enhanceExistingProgress(tabId, provider, currentChunk, totalChunks, startTime, customMessage = '') {
        // Only enhance if NOT Google (Google already has comprehensive system)
        if (provider === 'google') return;

        const config = this.providerConfigs[provider];
        if (!config) return;

        const elapsed = (Date.now() - startTime) / 1000;
        const progress = currentChunk / totalChunks;

        // Estimate remaining time for streaming providers (they're predictable)
        const avgTimePerChunk = elapsed / currentChunk;
        const estimatedRemaining = Math.ceil((totalChunks - currentChunk) * avgTimePerChunk);

        const enhancedMessage = customMessage || this.getProgressMessage(provider, {
            currentChunk,
            totalChunks,
            elapsed,
            estimatedRemaining
        });

        // Send enhanced progress update using existing system
        try {
            chrome.tabs.sendMessage(tabId, {
                type: 'enhanced-progress-update',
                data: {
                    currentChunk,
                    totalChunks,
                    progress: Math.min(progress, 1),
                    estimatedRemaining: Math.max(estimatedRemaining, 0),
                    elapsed: Math.floor(elapsed),
                    status: enhancedMessage,
                    provider: provider,
                    color: config.color,
                    isStreamingProvider: true
                }
            }).catch(() => { });
        } catch (error) {
            // Silently fail - don't break existing functionality
        }
    }

    static async trackProviderPerformance(provider, duration, textLength, success) {
        // Store performance metrics for future optimization
        try {
            const { providerMetrics } = await chrome.storage.local.get(['providerMetrics']);
            const metrics = providerMetrics || {};

            if (!metrics[provider]) {
                metrics[provider] = [];
            }

            metrics[provider].push({
                timestamp: Date.now(),
                duration,
                textLength,
                success,
                charactersPerSecond: textLength / (duration / 1000)
            });

            // Keep only last 10 metrics per provider
            if (metrics[provider].length > 10) {
                metrics[provider] = metrics[provider].slice(-10);
            }

            await chrome.storage.local.set({ providerMetrics: metrics });
        } catch (error) {
            // Silently fail - don't break existing functionality
        }
    }

    static getProviderStats(provider) {
        return chrome.storage.local.get(['providerMetrics']).then(({ providerMetrics }) => {
            const metrics = providerMetrics?.[provider] || [];
            if (metrics.length === 0) return null;

            const avgSpeed = metrics.reduce((sum, m) => sum + m.charactersPerSecond, 0) / metrics.length;
            const successRate = metrics.filter(m => m.success).length / metrics.length;

            return {
                averageSpeed: Math.round(avgSpeed),
                successRate: Math.round(successRate * 100),
                totalSamples: metrics.length
            };
        });
    }
}

// Enhanced progress messages for different scenarios
const ProgressMessages = {
    // Streaming providers get encouraging real-time messages
    streaming: {
        start: (provider) => `🚀 ${provider} is preparing your audio...`,
        processing: (provider, chunk, total) => `🎵 ${provider} streaming chunk ${chunk} of ${total}`,
        almostDone: (provider) => `✨ ${provider} is finishing up...`,
        complete: (provider) => `✅ ${provider} audio ready!`
    },

    // Google gets existing detailed messages
    batch: {
        analyzing: 'Analyzing text complexity...',
        chunking: 'Splitting text into optimal chunks...',
        processing: 'Processing with Google TTS...',
        heartbeat: 'Processing large text - please wait...',
        complete: 'Processing complete! Starting playback...'
    }
};

export { UniversalProgressTracker, ProgressMessages };