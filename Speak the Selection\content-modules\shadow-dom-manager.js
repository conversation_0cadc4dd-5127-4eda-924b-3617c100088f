/**
 * Shadow DOM Manager Module
 * Handles Shadow DOM creation, player lifecycle, theme management, and CSS injection
 */

import { TTS_CONFIG } from './tts-config.js';
import { MemoryManager } from './memory-manager.js';
import { ThemeManager } from './theme-manager.js';

export class ShadowDOMManager {
    constructor() {
        this.shadowHost = null;
        this.shadowRoot = null;
        this.playerContainer = null;
        this.playerInitialized = false;
        this.playerInitPromise = null;
        this.currentRange = null;
        this.themeManager = new ThemeManager();
    }

    /**
     * Initialize the player if not already created
     */
    async ensurePlayerCreated() {
        if (this.playerInitialized) {
            return;
        }
        if (this.playerInitPromise) return this.playerInitPromise;
        this.playerInitPromise = this.createPlayer();
        await this.playerInitPromise;
        this.playerInitPromise = null;
    }

    /**
     * Main player creation function
     */
    async createPlayer() {
        if (this.playerInitialized) return;

        try {
            // Inject global highlight styles using configuration
            this.injectHighlightStyles();

            // Create shadow host
            this.createShadowHost();

            // Attach shadow root
            this.shadowRoot = this.shadowHost.attachShadow({ mode: 'open' });

            // Load and inject CSS
            await this.loadAndInjectCSS();

            // Create player container with HTML
            this.createPlayerContainer();

            this.playerInitialized = true;

        } catch (error) {
            console.error('Failed to create player:', error);
            if (this.shadowHost) this.shadowHost.remove();
            throw error;
        }
    }

    /**
     * Inject global highlight styles into document head
     */
    injectHighlightStyles() {
        if (!document.getElementById('tts-highlight-styles')) {
            const { highlighting } = TTS_CONFIG;
            const highlightStyles = document.createElement('style');
            highlightStyles.id = 'tts-highlight-styles';
            highlightStyles.textContent = `
                .tts-highlight {
                    background: ${highlighting.normalColor} !important;
                    color: #111 !important;
                    border-radius: ${highlighting.borderRadius} !important;
                    box-shadow: 0 0 0 1px rgba(255, 220, 80, 0.6) inset !important;
                    transition: ${highlighting.transition} !important;
                    /* Remove padding to prevent layout shifts */
                    padding: 0 !important;
                    margin: 0 !important;
                    /* Use box-shadow for visual separation instead of padding */
                    box-shadow:
                        0 0 0 1px rgba(255, 220, 80, 0.6) inset,
                        0 0 0 2px ${highlighting.normalColor} !important;
                }

                /* Overlay highlighting for list content - matches regular highlighting */
                .tts-list-highlight-overlay {
                    position: absolute !important;
                    background: rgba(255, 236, 138, ${highlighting.listOpacity}) !important;
                    border-radius: ${highlighting.borderRadius} !important;
                    box-shadow: 0 0 0 1px ${highlighting.borderColor} inset !important;
                    pointer-events: none !important;
                    z-index: ${highlighting.zIndex} !important;
                    transition: ${highlighting.transition} !important;
                }

                @media (prefers-reduced-motion: reduce) {
                    .tts-highlight,
                    .tts-list-highlight-overlay {
                        transition: none !important;
                    }
                }
            `;
            document.head.appendChild(highlightStyles);
        }
    }

    /**
     * Create the shadow host element
     */
    createShadowHost() {
        this.shadowHost = document.createElement('div');
        this.shadowHost.id = 'tts-player-shadow-host';
        this.shadowHost.style.cssText = `
            position: fixed !important;
            z-index: 2147483647 !important;
            display: none;  /* START HIDDEN */
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
            width: 128px !important;
            height: 56px !important;
            min-width: 48px !important;
            min-height: 48px !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            outline: none !important;
            box-sizing: border-box !important;
            background: none !important;
            isolation: isolate !important;
            border-radius: 28px !important;
        `;
        document.body.appendChild(this.shadowHost);
    }

    /**
     * Load CSS files and inject into shadow root
     */
    async loadAndInjectCSS() {
        const playerCssUrl = chrome.runtime.getURL('player.css');
        const loadingCssUrl = chrome.runtime.getURL('loading.css');

        try {
            const [playerCss, loadingCss] = await Promise.all([
                fetch(playerCssUrl).then(res =>
                    res.ok ? res.text() : Promise.reject(`Failed to load player.css: ${res.statusText}`)
                ),
                fetch(loadingCssUrl).then(res =>
                    res.ok ? res.text() : Promise.reject(`Failed to load loading.css: ${res.statusText}`)
                )
            ]);

            // Create and inject style tag
            const styleTag = document.createElement('style');
            styleTag.textContent = `${playerCss}\n${loadingCss}`;
            this.shadowRoot.appendChild(styleTag);

        } catch (error) {
            console.error('Failed to load CSS:', error);
            this.shadowHost.remove();
            throw error;
        }
    }

    /**
     * Create the player container with all UI elements
     */
    createPlayerContainer() {
        this.playerContainer = document.createElement('div');
        this.playerContainer.id = 'tts-player-container';
        this.playerContainer.className = 'minimized';
        this.playerContainer.innerHTML = `
            <div class="tts-minimized-player">
                <div class="tts-drag-handle"></div>
                <button class="tts-play-pause-button" aria-label="Play" data-state="play">
                    <svg class="icon" viewBox="0 0 24 24" width="18" height="18" aria-hidden="true" focusable="false">
                        <path class="icon-shape" d="M8 5v14l11-7z"></path>
                    </svg>
                </button>
                <button class="tts-close-button" aria-label="Close">×</button>
            </div>
            <div class="tts-expanded-player">
                <div class="tts-drag-handle"></div>
                <button class="tts-close-button" aria-label="Close">×</button>
                <div class="tts-expanded-left-panel">
                    <div class="tts-expanded-top-row">
                        <button class="tts-play-pause-button" aria-label="Play" data-state="play">
                            <svg class="icon" viewBox="0 0 24 24" width="18" height="18" aria-hidden="true" focusable="false">
                                <path class="icon-shape" d="M8 5v14l11-7z"></path>
                            </svg>
                        </button>
                        <div class="tts-provider-container">
                            <span class="tts-provider-label"></span>
                            <button class="tts-reset-speed-button">↺</button>
                        </div>
                    </div>
                    <div class="tts-progress-container">
                        <input type="range" class="tts-progress-slider" min="0" max="100" value="0">
                        <span class="tts-time-label">0:00 / 0:00</span>
                    </div>
                </div>
                <div class="tts-right-panel">
                    <div class="tts-vertical-slider-container">
                        <span class="tts-volume-value">50</span>
                        <input type="range" class="tts-volume-slider tts-vertical-slider" min="0" max="1" step="0.01" value="0.5">
                        <label>Vol</label>
                    </div>
                    <div class="tts-vertical-slider-container">
                        <span class="tts-speed-value">1.0x</span>
                        <input type="range" class="tts-speed-slider tts-vertical-slider" min="0.5" max="2" step="0.1" value="1.0">
                        <label>Spd</label>
                    </div>
                </div>
            </div>
            <div class="tts-loading-player">
                <div class="tts-loader"></div>
            </div>
        `;
        this.shadowRoot.appendChild(this.playerContainer);

        // Set the player container reference in theme manager
        this.themeManager.setPlayerContainer(this.playerContainer);

        // Set up event listeners for player controls
        this.setupPlayerEventListeners();
    }

    /**
     * Set up event listeners for player controls
     */
    setupPlayerEventListeners() {
        if (!this.playerContainer) return;

        // Play/pause button handlers
        const playPauseButtons = this.playerContainer.querySelectorAll('.tts-play-pause-button');
        playPauseButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handlePlayPauseClick();
            });
        });

        // Close button handlers
        const closeButtons = this.playerContainer.querySelectorAll('.tts-close-button');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleCloseClick();
            });
        });

        // Volume slider handlers
        const volumeSliders = this.playerContainer.querySelectorAll('.tts-volume-slider');
        volumeSliders.forEach(slider => {
            slider.addEventListener('input', (e) => {
                this.handleVolumeChange(parseFloat(e.target.value));
            });
        });

        // Speed slider handlers
        const speedSliders = this.playerContainer.querySelectorAll('.tts-speed-slider');
        speedSliders.forEach(slider => {
            slider.addEventListener('input', (e) => {
                this.handleSpeedChange(parseFloat(e.target.value));
            });
        });

        // Progress slider handlers
        const progressSliders = this.playerContainer.querySelectorAll('.tts-progress-slider');
        progressSliders.forEach(slider => {
            slider.addEventListener('input', (e) => {
                this.handleProgressChange(parseFloat(e.target.value));
            });
        });

        // Reset speed button handlers
        const resetSpeedButtons = this.playerContainer.querySelectorAll('.tts-reset-speed-button');
        resetSpeedButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleResetSpeed();
            });
        });
    }

    /**
     * Handle play/pause button click
     */
    handlePlayPauseClick() {
        // Get current selection text
        const selection = window.getSelection();
        const text = selection.toString().trim();

        if (!text) {
            alert('Please select some text to read aloud.');
            return;
        }

        // Send read-text message to background script
        chrome.runtime.sendMessage({
            type: 'read-text',
            text: text
        }).catch(error => {
            console.error('Failed to send read-text message:', error);
            alert('Failed to start text-to-speech. Please try again.');
        });
    }

    /**
     * Handle close button click
     */
    handleCloseClick() {
        // Send message to event handler to close player
        chrome.runtime.sendMessage({ type: 'close-player' }).catch(() => {
            // Fallback: hide player directly
            if (this.shadowHost) {
                this.shadowHost.style.display = 'none';
            }
        });
    }

    /**
     * Handle volume change
     */
    handleVolumeChange(volume) {
        chrome.runtime.sendMessage({
            type: 'set-volume',
            volume: volume
        }).catch(error => {
            console.error('Failed to set volume:', error);
        });

        // Update volume display
        const volumeValues = this.playerContainer.querySelectorAll('.tts-volume-value');
        volumeValues.forEach(span => {
            span.textContent = Math.round(volume * 100);
        });
    }

    /**
     * Handle speed change
     */
    handleSpeedChange(speed) {
        chrome.runtime.sendMessage({
            type: 'set-speed',
            speed: speed
        }).catch(error => {
            console.error('Failed to set speed:', error);
        });

        // Update speed display
        const speedValues = this.playerContainer.querySelectorAll('.tts-speed-value');
        speedValues.forEach(span => {
            span.textContent = `${speed.toFixed(1)}x`;
        });
    }

    /**
     * Handle progress change
     */
    handleProgressChange(progress) {
        chrome.runtime.sendMessage({
            type: 'seek-audio',
            time: progress
        }).catch(error => {
            console.error('Failed to seek audio:', error);
        });
    }

    /**
     * Handle reset speed button click
     */
    handleResetSpeed() {
        const defaultSpeed = 1.0;

        // Update speed sliders
        const speedSliders = this.playerContainer.querySelectorAll('.tts-speed-slider');
        speedSliders.forEach(slider => {
            slider.value = defaultSpeed;
        });

        // Update speed displays
        const speedValues = this.playerContainer.querySelectorAll('.tts-speed-value');
        speedValues.forEach(span => {
            span.textContent = `${defaultSpeed.toFixed(1)}x`;
        });

        // Send speed change message
        this.handleSpeedChange(defaultSpeed);
    }

    /**
     * Theme detection and management (delegated to theme manager)
     */
    detectCurrentTheme(range) {
        return this.themeManager.detectCurrentTheme(range);
    }

    /**
     * Apply theme to player (delegated to theme manager)
     */
    applyThemeToPlayer(theme) {
        return this.themeManager.applyThemeToPlayer(theme);
    }

    /**
     * Remove player and clean up resources
     */
    removePlayer() {
        if (this.shadowHost) {
            this.shadowHost.remove();
        }
        this.shadowHost = null;
        this.shadowRoot = null;
        this.playerContainer = null;
        this.playerInitialized = false;
    }

    /**
     * Show the player
     */
    showPlayer() {
        if (this.shadowHost) {
            this.shadowHost.style.display = 'block';
        }
    }

    /**
     * Hide the player
     */
    hidePlayer() {
        if (this.shadowHost) {
            this.shadowHost.style.display = 'none';
        }
    }

    /**
     * Set player state (minimized, expanded, loading)
     */
    setPlayerState(state) {
        if (this.playerContainer) {
            this.playerContainer.className = state;
        }
    }

    /**
     * Update theme setting
     */
    setTheme(theme) {
        this.themeManager.setTheme(theme);
    }

    /**
     * Set current range for theme detection
     */
    setCurrentRange(range) {
        this.currentRange = range;
        this.themeManager.setCurrentRange(range);
    }

    /**
     * Get references to important elements
     */
    getElements() {
        return {
            shadowHost: this.shadowHost,
            shadowRoot: this.shadowRoot,
            playerContainer: this.playerContainer,
            themeManager: this.themeManager
        };
    }

    /**
     * Check if player is initialized
     */
    isInitialized() {
        return this.playerInitialized;
    }

    /**
     * Debounce utility with memory tracking
     */
    debounce(func, delay, name = 'anonymous') {
        return function (...args) {
            const timeoutId = MemoryManager.trackTimer(setTimeout(() => {
                func.apply(this, args);
            }, delay));
            MemoryManager.trackDebouncedFunction(name, timeoutId);
        };
    }
}