const audioPlayer = document.getElementById('audio-player');
let port;

function connectPort() {
    port = chrome.runtime.connect({ name: 'offscreen' });
    port.onDisconnect.addListener(connectPort);
}

connectPort();

let audioQueue = [];
let isPlaying = false;
let currentTabId = null;
let currentEnableHighlighting = false;
let manualSeek = false;
let playbackState = 'idle'; // Track actual playback state

async function processQueue() {
    if (audioQueue.length === 0 || isPlaying) {
        return;
    }

    isPlaying = true;
    playbackState = 'playing';
    const item = audioQueue[0]; // Peek at the next item

    audioPlayer.src = item.audioUrl;

    try {
        await audioPlayer.play();
        if (currentEnableHighlighting) {
            sendHighlightMessage(item);
        }
    } catch (error) {
        console.warn('Audio playback error:', error.name, error.message);
        if (error.name !== 'AbortError') {
            // Only log non-AbortError issues
            console.error('Unexpected audio error:', error);
        }
        isPlaying = false;
        playbackState = 'idle';
        audioQueue.shift(); // Remove the failed item
        processQueue(); // Try next item
    }
}

function sendHighlightMessage(item, fromClipboard) {
    if (item.alignment) {
        port.postMessage({ type: 'highlight-text', alignment: item.alignment, text: item.text, fromClipboard: fromClipboard });
    } else {
        const audio = new Audio(item.audioUrl);
        audio.addEventListener('loadedmetadata', () => {
            port.postMessage({ type: 'highlight-text', text: item.text, audioDuration: audio.duration, fromClipboard: fromClipboard });
        });
    }
}

port.onMessage.addListener(async (msg) => {

    switch (msg.type) {
        case 'play-audio-queue':
            audioQueue = msg.queue;

            audioPlayer.volume = msg.volume;
            audioPlayer.playbackRate = msg.speed;
            currentTabId = msg.tabId;
            currentEnableHighlighting = msg.enableHighlighting;
            fromClipboard = msg.fromClipboard;
            processQueue();
            break;
        case 'pause-audio-offscreen':
            playbackState = 'pausing';
            audioPlayer.pause();
            break;
        case 'resume-audio-offscreen':
            // Add error handling for play() promise to prevent AbortError
            try {
                await audioPlayer.play();
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.warn('Audio resume failed:', error.name, error.message);
                }
                // AbortError is normal when play() is interrupted by pause() - ignore it
            }
            break;
        case 'set-volume-offscreen':
            audioPlayer.volume = msg.volume;
            break;
        case 'set-speed-offscreen':
            audioPlayer.playbackRate = msg.speed;
            break;
        case 'seek-audio-offscreen':
            if (audioPlayer.duration) {
                manualSeek = true;
                // Pause before seeking to prevent AbortError
                const wasPlaying = !audioPlayer.paused;
                if (wasPlaying) {
                    audioPlayer.pause();
                }
                audioPlayer.currentTime = msg.time;
                // Resume playback if it was playing before seek
                if (wasPlaying) {
                    try {
                        await audioPlayer.play();
                    } catch (error) {
                        if (error.name !== 'AbortError') {
                            console.warn('Audio seek resume failed:', error.name, error.message);
                        }
                        // AbortError is normal - ignore it
                    }
                }
            }
            break;
        case 'get-audio-duration':
            const audio = new Audio(msg.url);
            audio.addEventListener('loadedmetadata', () => {
                port.postMessage({ type: 'audio-duration-response', url: msg.url, duration: audio.duration });
            });
            audio.addEventListener('error', () => {
                port.postMessage({ type: 'audio-duration-response', url: msg.url, error: 'Failed to load audio metadata.' });
            });
            break;
        case 'stop-audio':
            audioQueue = [];
            isPlaying = false;
            playbackState = 'stopping';
            if (!audioPlayer.paused) {
                audioPlayer.pause();
                audioPlayer.src = ''; // Stop current playback
            }
            playbackState = 'idle';
            break;
        case 'get-clipboard-text':
            {
                const textarea = document.createElement('textarea');
                document.body.appendChild(textarea);
                textarea.focus();
                document.execCommand('paste');
                const text = textarea.value;
                document.body.removeChild(textarea);
                port.postMessage({ type: 'clipboard-text-response', text: text });
            }
            break;
        case 'get-audio-state':
            // Return current audio player state
            const actualState = audioPlayer.paused ? 'paused' :
                audioPlayer.ended ? 'ended' :
                    audioPlayer.readyState >= 2 && !audioPlayer.paused ? 'playing' : 'idle';
            port.postMessage({
                type: 'audio-state-response',
                state: actualState,
                currentTime: audioPlayer.currentTime,
                duration: audioPlayer.duration,
                isPlaying: !audioPlayer.paused && !audioPlayer.ended
            });
            break;
    }
});

audioPlayer.addEventListener('play', () => {
    playbackState = 'playing';
    port.postMessage({ type: 'playback-state-changed', state: 'playing' });
    manualSeek = false;
});

audioPlayer.addEventListener('pause', () => {
    playbackState = 'paused';
    port.postMessage({ type: 'playback-state-changed', state: 'paused' });
});

audioPlayer.addEventListener('ended', () => {
    isPlaying = false;
    playbackState = 'ended';
    if (audioQueue.length > 0) {
        audioQueue.shift(); // Remove the completed item
    }
    if (audioQueue.length > 0) {
        playbackState = 'idle';
        processQueue(); // Play the next item
    } else {
        if (!manualSeek) {
            port.postMessage({ type: 'playback-finished' });
        }
        playbackState = 'idle';
        manualSeek = false; // Reset flag after use
    }
});

audioPlayer.addEventListener('timeupdate', () => {
    port.postMessage({
        type: 'playback-progress-update',
        currentTime: audioPlayer.currentTime,
        duration: audioPlayer.duration
    });
});
