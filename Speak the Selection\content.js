/**
 * Main Content Script for TTS Extension
 * Orchestrates all modular components for text-to-speech functionality
 */

// Import all modules using chrome-extension URL format
import { TTS_CONFIG } from './content-modules/tts-config.js';
import { MemoryManager } from './content-modules/memory-manager.js';
import { ShadowDOMManager } from './content-modules/shadow-dom-manager.js';
import { PlayerManager } from './content-modules/player-manager.js';
import { TextSelectionManager } from './content-modules/text-selection-manager.js';
import { HighlightingManager } from './content-modules/highlighting-manager.js';
import { FreeWordAligner } from './content-modules/word-aligner.js';
import { AudioStateManager } from './content-modules/audio-state-manager.js';
import { ThemeManager } from './content-modules/theme-manager.js';
import { ProgressOverlayManager } from './content-modules/progress-overlay-manager.js';
import { SimpleLoaderManager } from './content-modules/simple-loader-manager.js';
import { EventHandler } from './content-modules/event-handler.js';
import { PlaybackState } from './shared/constants.js';

// Initialize all modules
const memoryManager = new MemoryManager();
const shadowDOMManager = new ShadowDOMManager();
const playerManager = new PlayerManager();
const textSelectionManager = new TextSelectionManager();
const highlightingManager = new HighlightingManager();
const audioStateManager = new AudioStateManager();
const themeManager = new ThemeManager();
const progressOverlayManager = new ProgressOverlayManager(themeManager);
const simpleLoaderManager = new SimpleLoaderManager(themeManager);

// Initialize event handler with all modules
const eventHandler = new EventHandler({
    shadowDOMManager,
    audioStateManager,
    textSelectionManager,
    highlightingManager,
    progressOverlayManager,
    simpleLoaderManager,
    wordAligner: FreeWordAligner
});

// Initialize the extension
function initializeExtension() {
    // Initialize event handlers
    eventHandler.initialize();

    // Initialize audio state manager
    audioStateManager.initialize();

    // Set up any additional initialization logic
    console.log('TTS Extension initialized with modular architecture');
}

// Clean up resources when needed
function cleanup() {
    // Clean up all managers
    memoryManager.cleanup();
    shadowDOMManager.removePlayer();
    highlightingManager.clearHighlights();
    progressOverlayManager.cleanup();
    simpleLoaderManager.cleanup();

    console.log('TTS Extension cleaned up');
}

// Handle page unload/cleanup
window.addEventListener('beforeunload', cleanup);

// Initialize the extension when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeExtension);
} else {
    initializeExtension();
}

// Export for potential debugging access
window.TTSExtension = {
    memoryManager,
    shadowDOMManager,
    playerManager,
    textSelectionManager,
    highlightingManager,
    audioStateManager,
    themeManager,
    progressOverlayManager,
    simpleLoaderManager,
    eventHandler,
    cleanup
};

// Expose constants
window.PlaybackState = PlaybackState;