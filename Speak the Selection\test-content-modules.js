/**
 * Test file for content.js modular refactoring
 * Validates that all modules are properly imported and functioning
 */

// Mock Chrome API for testing
const mockChrome = {
    runtime: {
        sendMessage: (message, callback) => {
            console.log('Mock sendMessage:', message);
            if (callback) callback({ success: true });
        },
        onMessage: {
            addListener: (listener) => {
                console.log('Mock onMessage listener added');
            }
        }
    },
    storage: {
        sync: {
            get: (keys, callback) => {
                console.log('Mock storage get:', keys);
                if (callback) callback({});
            },
            set: (items, callback) => {
                console.log('Mock storage set:', items);
                if (callback) callback();
            }
        },
        local: {
            get: (keys, callback) => {
                console.log('Mock local storage get:', keys);
                if (callback) callback({});
            }
        },
        onChanged: {
            addListener: (listener) => {
                console.log('Mock storage onChanged listener added');
            }
        }
    }
};

// Mock global objects
global.chrome = mockChrome;
global.window = {
    addEventListener: (event, handler) => {
        console.log(`Mock window event listener added for ${event}`);
    },
    document: {
        addEventListener: (event, handler) => {
            console.log(`Mock document event listener added for ${event}`);
        },
        createElement: (tag) => ({
            tagName: tag,
            style: {},
            setAttribute: (name, value) => {
                console.log(`Mock setAttribute: ${name} = ${value}`);
            },
            appendChild: (child) => {
                console.log(`Mock appendChild: ${child.tagName || child}`);
            },
            querySelector: () => null,
            querySelectorAll: () => []
        }),
        head: {
            appendChild: (element) => {
                console.log(`Mock head appendChild: ${element.tagName || element}`);
            }
        },
        body: {
            appendChild: (element) => {
                console.log(`Mock body appendChild: ${element.tagName || element}`);
            }
        }
    },
    location: {
        hostname: 'test.example.com'
    },
    getComputedStyle: () => ({
        backgroundColor: 'rgba(0, 0, 0, 0)'
    }),
    matchMedia: () => ({ matches: false }),
    innerWidth: 1920,
    innerHeight: 1080,
    scrollY: 0,
    scrollX: 0
};

global.document = global.window.document;
global.fetch = (url) => Promise.resolve({
    ok: true,
    text: () => Promise.resolve('/* mock css */')
});

// Test imports
try {
    console.log('Testing module imports...');

    // Import the main content script
    import('../content.js').then(() => {
        console.log('✓ Main content.js imported successfully');

        // Test that global objects are available
        if (typeof window.TTSExtension !== 'undefined') {
            console.log('✓ TTSExtension global object is available');

            // Test individual modules
            const modules = [
                'memoryManager',
                'shadowDOMManager',
                'playerManager',
                'textSelectionManager',
                'highlightingManager',
                'audioStateManager',
                'themeManager',
                'progressOverlayManager',
                'simpleLoaderManager',
                'eventHandler'
            ];

            let allModulesPresent = true;
            modules.forEach(module => {
                if (typeof window.TTSExtension[module] !== 'undefined') {
                    console.log(`✓ ${module} is available`);
                } else {
                    console.log(`✗ ${module} is missing`);
                    allModulesPresent = false;
                }
            });

            if (allModulesPresent) {
                console.log('✓ All modules are properly exported');
            } else {
                console.log('✗ Some modules are missing from global export');
            }

            // Test constants
            if (typeof window.PlaybackState !== 'undefined') {
                console.log('✓ PlaybackState constant is available');
            } else {
                console.log('✗ PlaybackState constant is missing');
            }

        } else {
            console.log('✗ TTSExtension global object is not available');
        }

        console.log('Module import test completed');
    }).catch(error => {
        console.error('✗ Failed to import content.js:', error);
    });

} catch (error) {
    console.error('Test failed with error:', error);
}