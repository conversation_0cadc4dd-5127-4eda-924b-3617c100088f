/**
 * Simple Loader Manager Module
 * Handles simple loading indicators and progress displays when floating button is disabled
 */

export class SimpleLoaderManager {
    constructor(themeManager) {
        this.themeManager = themeManager;
        this.simpleLoader = null;
    }

    /**
     * Show simple loader with theme-aware styling
     */
    showSimpleLoader(currentRange = null) {
        if (!this.simpleLoader) {
            this.simpleLoader = document.createElement('div');
            this.simpleLoader.className = 'tts-simple-loader';

            // Get current theme for loader styling
            const themeStyles = this.themeManager ?
                this.themeManager.getLoaderThemeStyles(currentRange) :
                { theme: 'dark', isLight: false, loaderBg: 'rgba(0, 0, 0, 0.5)', loaderBorder: '#fff', shadowColor: 'rgba(255, 255, 255, 0.4)' };

            const { loaderBg, loaderBorder, shadowColor } = themeStyles;

            // Ensure the styles from loading.css are applied
            this.simpleLoader.style.cssText = `
                position: fixed !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                z-index: 2147483647 !important;
                width: 48px !important;
                height: 48px !important;
                border-radius: 50% !important;
                background: ${loaderBg} !important;
                border: 3px solid ${loaderBorder} !important;
                border-top: 3px solid transparent !important;
                animation: tts-simple-spin 1s linear infinite !important;
                box-shadow: 0 4px 20px ${shadowColor} !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 12px !important;
                color: ${loaderBorder} !important;
                font-weight: 600 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
            `;

            // Add keyframe animation if not already present
            if (!document.getElementById('tts-simple-loader-keyframes')) {
                const style = document.createElement('style');
                style.id = 'tts-simple-loader-keyframes';
                style.textContent = `
                    @keyframes tts-simple-spin {
                        0% { transform: translate(-50%, -50%) rotate(0deg); }
                        100% { transform: translate(-50%, -50%) rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(this.simpleLoader);
        }

        this.simpleLoader.style.display = 'flex';
        this.simpleLoader.classList.remove('hidden');
    }

    /**
     * Hide simple loader
     */
    hideSimpleLoader() {
        if (this.simpleLoader) {
            this.simpleLoader.style.display = 'none';
            this.simpleLoader.classList.add('hidden');

            // Remove from DOM after a short delay to allow any animations to complete
            setTimeout(() => {
                if (this.simpleLoader && this.simpleLoader.parentNode) {
                    this.simpleLoader.parentNode.removeChild(this.simpleLoader);
                    this.simpleLoader = null;
                }
            }, 100);
        }
    }

    /**
     * Check if simple loader is currently showing
     */
    isLoaderVisible() {
        return this.simpleLoader && this.simpleLoader.style.display !== 'none';
    }

    /**
     * Update loader theme
     */
    updateLoaderTheme(currentRange = null) {
        if (!this.simpleLoader || !this.themeManager) return;

        const themeStyles = this.themeManager.getLoaderThemeStyles(currentRange);
        const { loaderBg, loaderBorder, shadowColor } = themeStyles;

        this.simpleLoader.style.background = loaderBg;
        this.simpleLoader.style.borderColor = loaderBorder;
        this.simpleLoader.style.borderTopColor = 'transparent';
        this.simpleLoader.style.boxShadow = `0 4px 20px ${shadowColor}`;
        this.simpleLoader.style.color = loaderBorder;
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.hideSimpleLoader();
    }
}