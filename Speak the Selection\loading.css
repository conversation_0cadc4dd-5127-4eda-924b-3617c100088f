.tts-simple-loader {
    position: absolute;
    z-index: 2147483647;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    box-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(255, 255, 255, 0.6),
        0 0 30px rgba(255, 255, 255, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.tts-simple-loader.hidden {
    display: none;
}

.tts-simple-loader::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: tts-spin 1s linear infinite;
}

@keyframes tts-spin {
    to {
        transform: rotate(360deg);
    }
}