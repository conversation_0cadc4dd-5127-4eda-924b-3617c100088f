/**
 * Highlighting Manager Module
 * Handles text highlighting, word tracking, overlay management, and list content highlighting
 */

export class HighlightingManager {
    constructor() {
        this.wordSpans = [];
        this.lastHighlightedSpan = null;
        this.highlightContainer = null;
        this.currentRange = null;
    }

    /**
     * Initialize highlighting manager
     */
    initialize() {
        // Initialize any required state
        this.resetState();
    }

    /**
     * Handle character-based highlighting alignment
     */
    handleHighlighting(alignment, currentRange) {
        if (!alignment || !alignment.characters || !alignment.character_start_times_seconds) return;
        if (!currentRange) return;

        this.currentRange = currentRange;

        const words = [];
        let currentWord = '';
        let wordStartTime = 0;

        alignment.characters.forEach((char, i) => {
            if (char !== ' ') {
                if (currentWord === '') wordStartTime = alignment.character_start_times_seconds[i];
                currentWord += char;
            } else {
                if (currentWord !== '') {
                    words.push({ text: currentWord, start: wordStartTime });
                    currentWord = '';
                }
            }
        });
        if (currentWord !== '') words.push({ text: currentWord, start: wordStartTime });

        this.wordSpans = this.wrapWordsAndPreserveStructure(currentRange, words);
        this.lastHighlightedSpan = null;
    }

    /**
     * Handle free highlighting for estimated timing
     */
    handleFreeHighlighting(alignment, currentRange) {
        if (!currentRange) return;

        this.currentRange = currentRange;
        this.wordSpans = this.wrapWordsAndPreserveStructure(
            currentRange,
            alignment.map(item => ({ start: item.start }))
        );
        this.lastHighlightedSpan = null;
    }

    /**
     * Update highlighting based on current playback time
     */
    updateHighlightAtTime(timeInSeconds) {
        if (!this.wordSpans || this.wordSpans.length === 0) return;

        // Check if we're dealing with virtual spans (list content)
        const hasVirtualSpans = this.wordSpans.some(span => span.isVirtual);

        if (hasVirtualSpans) {
            this.updateListHighlightAtTime(timeInSeconds);
            return;
        }

        // Original highlighting for non-list content
        let currentSpan = null;
        for (const span of this.wordSpans) {
            const startTime = parseFloat(span.dataset.startTime);
            if (timeInSeconds >= startTime) {
                currentSpan = span;
            } else {
                break;
            }
        }

        if (currentSpan && currentSpan !== this.lastHighlightedSpan) {
            // Remove highlight from previous span
            if (this.lastHighlightedSpan) {
                this.lastHighlightedSpan.classList.remove('tts-highlight');
            }

            // Add highlight to current span
            currentSpan.classList.add('tts-highlight');
            this.lastHighlightedSpan = currentSpan;

            // Scroll to the highlighted word
            currentSpan.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
        }
    }

    /**
     * Update list highlighting for virtual spans
     */
    updateListHighlightAtTime(timeInSeconds) {
        if (!window.ttsListHighlightData || !this.wordSpans) return;

        // Find current word based on timing
        let currentWordIndex = -1;
        for (let i = 0; i < this.wordSpans.length; i++) {
            const span = this.wordSpans[i];
            const startTime = parseFloat(span.dataset.startTime);
            if (timeInSeconds >= startTime) {
                currentWordIndex = i;
            } else {
                break;
            }
        }

        if (currentWordIndex >= 0 && currentWordIndex !== this.lastHighlightedSpan) {
            // Clear previous highlight
            this.clearListHighlight();

            // Apply new highlight
            this.applyListHighlight(currentWordIndex);
            this.lastHighlightedSpan = currentWordIndex;
        }
    }

    /**
     * Apply highlight to a specific word in list content
     */
    applyListHighlight(wordIndex) {
        if (!window.ttsListHighlightData) return;

        const words = window.ttsListHighlightData.words;
        const range = window.ttsListHighlightData.range;

        if (!words || !range || wordIndex >= words.length) return;

        try {
            const targetWord = words[wordIndex];
            const wordRect = this.findWordRectInRange(range, targetWord, wordIndex);

            if (wordRect) {
                this.createHighlightOverlay(wordRect);

                // Scroll to the highlighted word
                const scrollTarget = {
                    top: wordRect.top + window.scrollY,
                    behavior: 'smooth',
                    block: 'center'
                };

                window.scrollTo({
                    top: scrollTarget.top - window.innerHeight / 2,
                    behavior: 'smooth'
                });
            }
        } catch (e) {
            // Fallback: just scroll to the general area
            if (range.startContainer && range.startContainer.scrollIntoView) {
                range.startContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    /**
     * Find word rectangle in range for highlighting
     */
    findWordRectInRange(range, targetWord, wordIndex) {
        try {
            const rangeText = range.toString();
            const actualStartIndex = rangeText.indexOf(targetWord, wordIndex);

            if (actualStartIndex === -1) {
                return null;
            }

            // Create a new range for just this word
            const wordRange = document.createRange();

            // Find the text node and offset for the start position
            let currentPos = 0;
            const walker = document.createTreeWalker(
                range.commonAncestorContainer,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                if (!range.intersectsNode(node)) continue;

                let nodeText = node.textContent;
                let nodeStart = currentPos;

                // Adjust for range boundaries
                if (node === range.startContainer) {
                    nodeText = nodeText.substring(range.startOffset);
                    nodeStart = currentPos;
                }
                if (node === range.endContainer) {
                    const endOffset = node === range.startContainer ?
                        range.endOffset - range.startOffset :
                        range.endOffset;
                    nodeText = nodeText.substring(0, endOffset);
                }

                const nodeEnd = currentPos + nodeText.length;

                if (actualStartIndex >= nodeStart && actualStartIndex < nodeEnd) {
                    // Found the node containing our word start
                    const localOffset = (actualStartIndex - nodeStart) +
                        (node === range.startContainer ? range.startOffset : 0);
                    const wordEnd = Math.min(
                        localOffset + targetWord.length,
                        node.textContent.length
                    );

                    wordRange.setStart(node, localOffset);
                    wordRange.setEnd(node, wordEnd);

                    const rect = wordRange.getBoundingClientRect();

                    if (rect.width > 0 && rect.height > 0) {
                        return rect;
                    }
                }

                currentPos = nodeEnd;
            }

        } catch (e) {
            // Silently ignore errors
        }

        return null;
    }

    /**
     * Create highlight overlay for list content
     */
    createHighlightOverlay(rect) {
        // Remove any existing overlay
        this.removeHighlightOverlay();

        // Create new overlay element
        const overlay = document.createElement('div');
        overlay.className = 'tts-list-highlight-overlay';
        overlay.id = 'tts-current-highlight-overlay';

        // Position the overlay exactly over the word
        overlay.style.left = `${rect.left + window.scrollX}px`;
        overlay.style.top = `${rect.top + window.scrollY}px`;
        overlay.style.width = `${rect.width}px`;
        overlay.style.height = `${rect.height}px`;

        // Add to document
        document.body.appendChild(overlay);

        // Store reference for cleanup
        window.ttsCurrentOverlay = overlay;
    }

    /**
     * Remove highlight overlay
     */
    removeHighlightOverlay() {
        const existingOverlay = document.getElementById('tts-current-highlight-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        if (window.ttsCurrentOverlay) {
            try {
                window.ttsCurrentOverlay.remove();
            } catch (e) {
                // Silently ignore
            }
            delete window.ttsCurrentOverlay;
        }
    }

    /**
     * Clear list highlights
     */
    clearListHighlight() {
        // Remove any highlight overlays
        this.removeHighlightOverlay();

        // Clear any text selections
        try {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                selection.removeAllRanges();
            }
        } catch (e) {
            // Silently ignore errors
        }
    }

    /**
     * Clear all highlights and reset state
     */
    clearHighlights() {
        try {
            // Check if we have virtual spans (list content)
            const hasVirtualSpans = this.wordSpans.some(span => span && span.isVirtual);

            if (hasVirtualSpans) {
                // For virtual spans, clear overlays and data
                this.clearListHighlight();
                this.removeHighlightOverlay();
                if (window.ttsListHighlightData) {
                    delete window.ttsListHighlightData;
                }
            } else if (this.wordSpans.length > 0) {
                // For regular spans, do the normal cleanup
                const parents = new Set();
                this.wordSpans.forEach(span => {
                    if (span && span.parentNode) {
                        try {
                            parents.add(span.parentNode);
                            const textNode = document.createTextNode(span.textContent);
                            span.parentNode.replaceChild(textNode, span);
                        } catch (e) {
                            // Silently handle DOM manipulation errors
                        }
                    }
                });

                parents.forEach(parent => {
                    try {
                        // For list elements, be more careful about normalization
                        if (parent.tagName === 'LI' || parent.tagName === 'UL' || parent.tagName === 'OL') {
                            // Preserve the structure of list elements
                            parent.normalize();
                        } else {
                            parent.normalize();
                        }
                    } catch (e) {
                        // Silently ignore normalization errors
                    }
                });
            }
        } catch (e) {
            // Robust error handling for cleanup
            console.warn('Error during highlight cleanup:', e);
        } finally {
            // Always reset state regardless of errors
            this.resetState();
        }
    }

    /**
     * Reset all highlighting state
     */
    resetState() {
        this.highlightContainer = null;
        this.currentRange = null;
        this.wordSpans = [];
        this.lastHighlightedSpan = null;

        // Clean up any remaining global references
        if (window.ttsCurrentOverlay) {
            try {
                window.ttsCurrentOverlay.remove();
            } catch (e) {
                // Silently ignore
            }
            delete window.ttsCurrentOverlay;
        }
    }

    /**
     * Wrap words and preserve DOM structure
     */
    wrapWordsAndPreserveStructure(range, wordData) {
        const spans = [];
        let wordIndex = 0;

        // Helper functions
        const isListElement = (element) => {
            return element && (element.tagName === 'LI' || element.tagName === 'UL' || element.tagName === 'OL');
        };

        const hasListStyling = (element) => {
            if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;
            const style = window.getComputedStyle(element);
            return style.listStyleType !== 'none' || style.display === 'list-item';
        };

        const isInListContext = (node) => {
            let currentParent = node.parentNode;
            while (currentParent && currentParent !== document.body) {
                if (isListElement(currentParent) || hasListStyling(currentParent)) {
                    return currentParent;
                }
                currentParent = currentParent.parentNode;
            }
            return null;
        };

        const wrapTextNode = (node) => {
            const text = node.textContent;
            const wordsAndSpaces = text.split(/(\s+)/);
            const fragment = document.createDocumentFragment();

            wordsAndSpaces.forEach(part => {
                if (part.trim().length > 0) {
                    const span = document.createElement('span');
                    span.textContent = part;
                    if (wordData && wordData[wordIndex]) {
                        span.dataset.startTime = wordData[wordIndex].start;
                        spans.push(span);
                    }
                    fragment.appendChild(span);
                    wordIndex++;
                } else {
                    fragment.appendChild(document.createTextNode(part));
                }
            });
            return fragment;
        };

        const containsListElements = (range) => {
            const fragment = range.cloneContents();
            return fragment.querySelector('li, ul, ol') !== null;
        };

        const createVirtualSpansForLists = (range, wordData) => {
            // Create virtual spans for list content that enable highlighting without DOM modification
            const spans = [];
            const text = range.toString();
            const words = text.trim().split(/\s+/);

            // Store the range for CSS-based highlighting
            if (!window.ttsListHighlightData) {
                window.ttsListHighlightData = {};
            }

            window.ttsListHighlightData.range = range.cloneRange();
            window.ttsListHighlightData.text = text;
            window.ttsListHighlightData.words = words;

            // Create virtual spans with timing data
            if (wordData && wordData.length > 0) {
                wordData.forEach((data, index) => {
                    if (index < words.length) {
                        const virtualSpan = {
                            textContent: words[index],
                            dataset: { startTime: data.start },
                            isVirtual: true,
                            wordIndex: index,
                            classList: {
                                add: function () { },
                                remove: function () { }
                            }
                        };
                        spans.push(virtualSpan);
                    }
                });
            } else {
                // Fallback: create spans based on word splitting
                words.forEach((word, index) => {
                    const virtualSpan = {
                        textContent: word,
                        dataset: { startTime: index * 0.5 }, // Rough timing estimate
                        isVirtual: true,
                        wordIndex: index,
                        classList: {
                            add: function () { },
                            remove: function () { }
                        }
                    };
                    spans.push(virtualSpan);
                });
            }

            return spans;
        };

        // Check if the selection contains list elements
        if (containsListElements(range)) {
            // For selections containing lists, use CSS-based highlighting without DOM modification
            return createVirtualSpansForLists(range, wordData);
        }

        // For non-list content, use the original approach
        const safeTraverse = (node) => {
            if (node.nodeType === Node.TEXT_NODE) {
                const listParent = isInListContext(node);

                if (listParent) {
                    // For list content, preserve structure exactly
                    if (node.textContent.trim() === '') {
                        return document.createTextNode(node.textContent);
                    } else {
                        // Even for list text, avoid DOM modification
                        return document.createTextNode(node.textContent);
                    }
                } else {
                    // Not in a list - safe to wrap normally
                    return wrapTextNode(node);
                }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                // For list elements, preserve their exact structure
                if (isListElement(node)) {
                    const newNode = node.cloneNode(false);
                    Array.from(node.childNodes).forEach(child => {
                        const processedChild = safeTraverse(child);
                        newNode.appendChild(processedChild);
                    });
                    return newNode;
                } else {
                    // For non-list elements, process normally
                    const newNode = node.cloneNode(false);
                    Array.from(node.childNodes).forEach(child => {
                        const processedChild = safeTraverse(child);
                        newNode.appendChild(processedChild);
                    });
                    return newNode;
                }
            }
            return node.cloneNode(true);
        };

        // Use the safe traversal approach for all content
        const fragment = range.cloneContents();
        const processedFragment = document.createDocumentFragment();

        Array.from(fragment.childNodes).forEach(child => {
            processedFragment.appendChild(safeTraverse(child));
        });

        range.deleteContents();
        range.insertNode(processedFragment);

        return spans;
    }

    /**
     * Get current highlighting state
     */
    getHighlightingState() {
        return {
            wordSpans: this.wordSpans,
            lastHighlightedSpan: this.lastHighlightedSpan,
            hasActiveHighlighting: this.wordSpans.length > 0,
            isVirtualSpans: this.wordSpans.some(span => span && span.isVirtual)
        };
    }

    /**
     * Check if highlighting is active
     */
    hasActiveHighlighting() {
        return this.wordSpans.length > 0;
    }

    /**
     * Get word spans
     */
    getWordSpans() {
        return this.wordSpans;
    }

    /**
     * Set current range
     */
    setCurrentRange(range) {
        this.currentRange = range;
    }
}