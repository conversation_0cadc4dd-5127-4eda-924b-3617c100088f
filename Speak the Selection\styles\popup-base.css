/* Base Variables and Core Layout */
:root {
    --bg: #f7f8fb;
    --surface: #ffffff;
    --surface-2: #f2f5ff;
    --text: #111827;
    --muted: #6b7280;
    --border: #e5e7eb;

    --primary: #2563eb;
    /* blue-600 */
    --primary-500: #3b82f6;
    /* blue-500 */
    --primary-50: #eff6ff;
    /* blue-50 */
    --primary-700: #1d4ed8;
    /* blue-700 */
    --ring: rgba(37, 99, 235, .35);

    --success: #16a34a;
    --warning: #d97706;
    --danger: #dc2626;

    --radius: 10px;
    --shadow: 0 8px 24px rgba(16, 24, 40, 0.08);
    --inset: inset 0 1px 0 rgba(255, 255, 255, .6);
}

@media (prefers-color-scheme: dark) {
    :root {
        --bg: #0b1220;
        --surface: #0f172a;
        --surface-2: #0b132e;
        --text: #e5e7eb;
        --muted: #94a3b8;
        --border: #1f2a44;

        --primary: #60a5fa;
        /* blue-400 */
        --primary-500: #60a5fa;
        --primary-50: #0b1220;
        --primary-700: #3b82f6;
        --ring: rgba(96, 165, 250, .5);

        --shadow: 0 8px 24px rgba(0, 0, 0, 0.35);
        --inset: inset 0 1px 0 rgba(255, 255, 255, .05);
    }
}

* {
    box-sizing: border-box
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 320px;
    padding: 12px;
    margin: 0;
    color: var(--text);
    background:
        radial-gradient(900px 900px at -10% -10%, #eaf2ff 0%, transparent 55%),
        var(--bg);
}

/* Panels */
.tab-content {
    display: none
}

.tab-content.active {
    display: block
}

/* Headings */
h1,
h2 {
    font-size: 16px;
    color: var(--text);
    margin: 12px 0 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--border);
}

h1:first-child {
    margin-top: 0
}

/* Cards for settings groups */
.provider-settings,
#elevenlabs-voice-settings,
#hume-voice-settings,
#straico-voice-settings,
#text-to-audio {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 12px;
    margin-bottom: 8px;
    box-shadow: var(--shadow);
}

.setting-group {
    background: var(--surface-2);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
}