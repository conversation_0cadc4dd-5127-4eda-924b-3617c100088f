import { google } from './google-tts.js';

// API Optimization System
class APIOptimizer {
    static requestCache = new Map();
    static pendingRequests = new Map();
    static retryDelays = [1000, 2000, 4000]; // Exponential backoff

    static async optimizedFetch(url, options, cacheKey = null, cacheTime = 300000) {
        // Use cache if available and not expired
        if (cacheKey && this.requestCache.has(cacheKey)) {
            const { data, timestamp } = this.requestCache.get(cacheKey);
            if (Date.now() - timestamp < cacheTime) {
                return data;
            }
        }

        // Deduplicate concurrent requests
        const requestKey = cacheKey || `${url}-${JSON.stringify(options)}`;
        if (this.pendingRequests.has(requestKey)) {
            return this.pendingRequests.get(requestKey);
        }

        const request = this.retryFetch(url, options);
        this.pendingRequests.set(requestKey, request);

        try {
            const response = await request;

            // Cache successful responses
            if (cacheKey && response.ok) {
                const data = await response.clone().json().catch(() => response.clone());
                this.requestCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });

                // Limit cache size
                if (this.requestCache.size > 100) {
                    const oldestKey = this.requestCache.keys().next().value;
                    this.requestCache.delete(oldestKey);
                }
            }

            return response;
        } finally {
            this.pendingRequests.delete(requestKey);
        }
    }

    static async retryFetch(url, options, maxRetries = 3) {
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch(url, options);

                // Don't retry on client errors (4xx)
                if (response.status >= 400 && response.status < 500 && response.status !== 429) {
                    return response;
                }

                if (response.ok || attempt === maxRetries) {
                    return response;
                }

                throw new Error(`HTTP ${response.status}`);
            } catch (error) {
                lastError = error;

                if (attempt < maxRetries) {
                    const delay = this.retryDelays[attempt] || 4000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }

    static clearCache() {
        this.requestCache.clear();
        this.pendingRequests.clear();
    }
}

export const providers = {
    google: google,
    elevenlabs: {
        async process(text, settings, format = 'mp3') {
            if (!settings.elevenLabsApiKey || !settings.selectedVoiceId) {
                throw new Error('ElevenLabs API key and voice are required. Please click the "🔑 Get free API key" link in the extension popup to get your API key.');
            }
            const url = `https://api.elevenlabs.io/v1/text-to-speech/${settings.selectedVoiceId}`;
            const speed = Math.max(0.7, Math.min(1.2, settings.playbackSpeed || 1.0));
            const voice_settings = {
                stability: settings.stability !== undefined ? settings.stability : 0.5,
                similarity_boost: settings.similarityBoost !== undefined ? settings.similarityBoost : 0.75,
                style: settings.style !== undefined ? settings.style : 0,
                speed: speed
            };
            const body = {
                text: text,
                model_id: "eleven_multilingual_v2",
                voice_settings: voice_settings
            };
            const response = await APIOptimizer.optimizedFetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'xi-api-key': settings.elevenLabsApiKey },
                body: JSON.stringify(body)
            }, `elevenlabs-${settings.selectedVoiceId}-${text.substring(0, 50)}`, 60000);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorData.detail?.[0]?.msg || 'Unknown error'}`);
            }
            const blob = await response.blob();
            const reader = new FileReader();
            const dataUrl = await new Promise((resolve, reject) => {
                reader.onloadend = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
            return {
                audioUrl: dataUrl,
                alignment: null // Timestamps are not supported for non-MP3 formats
            };
        }
    },
    hume: {
        async process(text, settings, format = 'mp3') {
            if (!settings.humeApiKey) {
                throw new Error('Hume API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your API key.');
            }
            if (!settings.selectedHumeVoiceName) {
                throw new Error('Please select a Hume AI voice in the extension popup.');
            }

            const url = 'https://api.hume.ai/v0/tts/stream/file';
            const body = {
                utterances: [{
                    text: text,
                    voice: { name: settings.selectedHumeVoiceName, provider: "HUME_AI" },
                }]
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Hume-Api-Key': settings.humeApiKey
                },
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Hume API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const blob = await response.blob();
            const reader = new FileReader();
            const dataUrl = await new Promise((resolve, reject) => {
                reader.onloadend = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });

            return {
                audioUrl: dataUrl,
                alignment: null
            };
        }
    },
    straico: {
        async process(text, settings) {
            if (!settings.straicoApiKey || !settings.selectedStraicoVoiceId) {
                throw new Error('Straico API key and voice are required. Please click the "🔑 Get free API key" link in the extension popup to get your API key.');
            }
            const url = 'https://api.straico.com/v1/tts/create';
            const body = new URLSearchParams();
            const model = settings.selectedStraicoModel || 'tts-1'; // Default to tts-1 if not set
            body.append('model', model);
            body.append('voice_id', settings.selectedStraicoVoiceId);
            body.append('text', text);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': `Bearer ${settings.straicoApiKey}`
                },
                body: body
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                throw new Error(`Straico API Error: ${errorData.error}`);
            }

            const data = await response.json();
            if (data.success && data.data && data.data.audio) {
                return {
                    audioUrl: data.data.audio,
                    alignment: null // Straico API does not provide alignment data
                };
            } else {
                throw new Error('Invalid response from Straico API.');
            }
        }
    }
};
