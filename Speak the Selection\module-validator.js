/**
 * Module Validator for Chrome Extension
 * This file helps validate that ES6 modules are loading correctly
 */

console.log('Module validator loaded - checking if imports work properly');

// Test simple export
export const MODULE_VALIDATOR = {
    version: '1.0',
    timestamp: Date.now(),
    status: 'ACTIVE'
};

// Log validation message
console.log('Module validator exports defined successfully');

// This file can be imported by content.js as a test to ensure module loading works