/* Labels + inputs */
label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--text);
    font-size: 13px;
}

input[type="password"],
select,
textarea {
    width: 100%;
    padding: 9px 10px;
    margin-bottom: 8px;
    background: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
    border-radius: 8px;
    transition: box-shadow .15s ease, border-color .15s ease, background-color .15s ease;
    outline: 0;
}

textarea {
    min-height: 88px;
    resize: vertical
}

input::placeholder,
textarea::placeholder {
    color: var(--muted)
}

input:focus,
select:focus,
textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--ring);
}

select {
    appearance: none;
    background-image: linear-gradient(45deg, transparent 50%, var(--muted) 50%), linear-gradient(135deg, var(--muted) 50%, transparent 50%), linear-gradient(to right, transparent, transparent);
    background-position: calc(100% - 18px) calc(1em + 2px), calc(100% - 13px) calc(1em + 2px), 100% 0;
    background-size: 5px 5px, 5px 5px, 2.5em 2.5em;
    background-repeat: no-repeat
}

/* Checkboxes */
.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    background: var(--surface-2);
    border: 1px solid var(--border);
    padding: 8px 10px;
    border-radius: 8px;
}

.filter-group input[type="checkbox"] {
    margin: 0;
    width: auto;
    accent-color: var(--primary);
}

.filter-group label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--text)
}

/* Button-style toggles */
.toggle-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 14px;
    margin-bottom: 8px;
    background: var(--surface);
    border: 2px solid var(--border);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: var(--text);
    position: relative;
    overflow: hidden;
    user-select: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-button:hover {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--ring), 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
    background: var(--surface-2);
}

.toggle-button.active {
    background: linear-gradient(135deg, var(--primary-50), var(--surface));
    border-color: var(--border);
    box-shadow: var(--inset), 0 1px 3px rgba(37, 99, 235, 0.1);
}

.toggle-button.active:hover {
    background: linear-gradient(135deg, var(--primary-50), var(--surface-2));
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px var(--ring), 0 2px 6px rgba(0, 0, 0, 0.15);
}

@media (prefers-color-scheme: dark) {
    .toggle-button {
        border-color: #374151;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .toggle-button:hover {
        border-color: var(--primary-400);
    }

    .toggle-button.active {
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), var(--surface));
    }

    .toggle-button.active:hover {
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), var(--surface-2));
    }
}

.toggle-button .toggle-label {
    font-weight: 500;
    flex: 1;
    text-align: left;
    margin-left: 12px;
}

.toggle-button .toggle-indicator {
    width: 44px;
    height: 22px;
    background: #d1d5db;
    border: 1px solid #9ca3af;
    border-radius: 11px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    margin-right: 10px;
    order: -1;
}

@media (prefers-color-scheme: dark) {
    .toggle-button .toggle-indicator {
        background: #4b5563;
        border-color: #6b7280;
    }
}

.toggle-button .toggle-indicator::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@media (prefers-color-scheme: dark) {
    .toggle-button .toggle-indicator::after {
        background: #f3f4f6;
        border-color: #d1d5db;
    }
}

.toggle-button.active .toggle-indicator {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
    border-color: var(--primary-600);
}

.toggle-button.active .toggle-indicator::after {
    transform: translateX(20px);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.4);
    background: #ffffff;
    border-color: rgba(255, 255, 255, 0.8);
}

.toggle-button:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--ring);
}

/* Buttons */
button {
    width: 100%;
    padding: 10px 12px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 15px;
    color: #fff;
    background: linear-gradient(180deg, var(--primary-500), var(--primary));
    box-shadow: 0 2px 0 rgba(0, 0, 0, .04), 0 10px 18px rgba(37, 99, 235, .25);
    transition: transform .08s ease, box-shadow .15s ease, filter .15s ease, background .15s ease;
}

button:hover {
    filter: saturate(1.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 0 rgba(0, 0, 0, .04), 0 14px 22px rgba(37, 99, 235, .28);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 0 rgba(0, 0, 0, .04), 0 10px 18px rgba(37, 99, 235, .24);
}

button:disabled {
    opacity: .65;
    cursor: not-allowed;
    box-shadow: none;
}

/* Voice select styling improvements */
.setting-group select {
    margin-bottom: 8px;
}

.voice-status {
    margin-top: 2px;
}

/* Status text */
.voice-status {
    font-size: 12px;
    text-align: center;
    margin-top: 2px;
    min-height: 0px;
    color: var(--muted);
}

.voice-status {
    display: block;
    /* Make it a block to center text */
    color: white !important;
    /* Make text white */
}

/* ElevenLabs/Straico credits text */
#elevenlabs-credits,
#straico-credits,
#tta-info {
    color: var(--muted);
}

/* Sliders */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    background: transparent;
    margin: 6px 0 8px;
}

input[type="range"]::-webkit-slider-runnable-track {
    height: 6px;
    background: linear-gradient(90deg, var(--primary-500) 0, var(--primary-500) 0) left/0% 100% no-repeat,
        /* filled (we'll fake via thumb glow) */
        var(--border);
    border-radius: 999px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: var(--surface);
    border: 2px solid var(--primary);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, .12);
    margin-top: -5px;
    transition: box-shadow .15s ease, transform .05s ease;
}

input[type="range"]:hover::-webkit-slider-thumb {
    box-shadow: 0 0 0 6px rgba(37, 99, 235, .18);
}

input[type="range"]:active::-webkit-slider-thumb {
    transform: scale(0.96);
}

input[type="range"]::-moz-range-track {
    height: 6px;
    background: var(--border);
    border: none;
    border-radius: 999px;
}

input[type="range"]::-moz-range-thumb {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: var(--surface);
    border: 2px solid var(--primary);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, .12);
}

/* Loading spinner inside button */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    100% {
        transform: rotate(360deg)
    }
}

/* Tiny UI polish */
#speed-value,
#stability-value,
#similarity-value,
#style-value,
#hume-speed-value {
    font-variant-numeric: tabular-nums;
    color: var(--primary)
}

.api-key-link {
    display: block;
    font-size: 12px;
    margin-top: -6px;
    margin-bottom: 8px;
    text-align: right;
}

.api-key-link a {
    color: var(--primary);
    text-decoration: none;
}

.api-key-link a:hover {
    text-decoration: underline;
}

/* Textarea with clear button */
.textarea-container {
    position: relative;
    margin-bottom: 8px;
}

#clear-text-btn {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    padding: 0;
    font-size: 16px;
    line-height: 1;
    border-radius: 6px;
    background: var(--surface-2);
    color: var(--muted);
    border: 1px solid var(--border);
    box-shadow: none;
    display: none;
    /* Hidden by default */
}

#clear-text-btn:hover {
    background: var(--primary-50);
    color: var(--primary);
    transform: none;
    box-shadow: none;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: var(--primary);
}

input:focus+.slider {
    box-shadow: 0 0 1px var(--primary);
}

input:checked+.slider:before {
    transform: translateX(16px);
}

/* Glow effect */
input:checked+.slider {
    box-shadow: 0 0 8px 2px rgba(59, 130, 246, 0.7);
    /* Green glow */
}

input+.slider {
    box-shadow: 0 0 8px 2px rgba(220, 38, 38, 0.7);
    /* Red glow */
}

#disable-on-site-group:hover {
    border-color: var(--danger) !important;
    box-shadow: 0 0 8px var(--danger) !important;
}

#general-settings-wrapper:not(.enabled-glow) #disable-on-site-group:hover {
    border-color: var(--danger) !important;
    box-shadow: 0 0 8px var(--danger) !important;
}

#general-settings-wrapper.enabled-glow #disable-on-site-group:hover {
    border-color: var(--primary) !important;
    box-shadow: 0 0 8px var(--primary) !important;
}