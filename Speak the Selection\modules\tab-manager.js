/**
 * Tab Management Module
 * Handles tab switching functionality and preserves last active tab
 */

import { StorageManager } from './storage-manager.js';

export class TabManager {
    constructor(domElements) {
        this.dom = domElements;
    }

    /**
     * Initialize tab management with event listeners
     */
    initialize() {
        this.dom.tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.switchToTab(button.dataset.tab);
            });
        });
    }

    /**
     * Switch to a specific tab
     * @param {string} tabId - The ID of the tab to switch to
     */
    async switchToTab(tabId) {
        // Save the active tab
        await StorageManager.saveSync({ lastActiveTab: tabId });

        // Update UI
        this.dom.tabButtons.forEach(btn => btn.classList.remove('active'));
        const activeButton = document.querySelector(`.tab-button[data-tab="${tabId}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }

        this.dom.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === tabId);
        });

        // Trigger tab-specific updates
        if (tabId === 'text-to-audio') {
            // Dispatch event for text-to-audio module to update info
            document.dispatchEvent(new CustomEvent('tab-switched', {
                detail: { tabId }
            }));
        }
    }

    /**
     * Restore the last active tab from storage
     * @param {Object} syncData - Sync storage data
     */
    restoreLastActiveTab(syncData) {
        if (syncData.lastActiveTab) {
            const tabToActivate = document.querySelector(`.tab-button[data-tab="${syncData.lastActiveTab}"]`);
            if (tabToActivate) {
                this.dom.tabButtons.forEach(btn => btn.classList.remove('active'));
                tabToActivate.classList.add('active');

                this.dom.tabContents.forEach(content => {
                    content.classList.toggle('active', content.id === syncData.lastActiveTab);
                });
            }
        }
    }
}