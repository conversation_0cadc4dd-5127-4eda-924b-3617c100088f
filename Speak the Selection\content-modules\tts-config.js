/**
 * TTS Configuration Module
 * Centralizes all configuration constants and settings for the TTS extension
 */

export const TTS_CONFIG = {
    // Highlighting settings
    highlighting: {
        normalColor: '#ffec8a',
        listOpacity: 0.3,
        borderColor: 'rgba(255, 220, 80, 0.4)',
        borderRadius: '4px',
        transition: 'all .15s ease',
        zIndex: 1000
    },

    // Player settings
    player: {
        defaultWidth: 128,
        defaultHeight: 56,
        minWidth: 48,
        minHeight: 48,
        zIndex: 2147483647
    },

    // Timing settings
    timing: {
        debounceDelay: 50,
        leaveDelay: 200,
        scrollDelay: 10,
        resizeDelay: 100
    },

    // Selection settings
    selection: {
        maxTextLength: 4000,
        confirmLongText: true
    }
};

// Viewport configuration for responsive positioning
export const VIEWPORT_CONFIG = {
    mobile: { width: 768, padding: 8, buttonScale: 0.9 },
    tablet: { width: 1024, padding: 12, buttonScale: 0.95 },
    desktop: { width: Infinity, padding: 20, buttonScale: 1.0 }
};

/**
 * Get viewport configuration based on current window width
 * @returns {Object} Viewport configuration object
 */
export function getViewportConfig() {
    const width = window.innerWidth;
    if (width <= VIEWPORT_CONFIG.mobile.width) return { type: 'mobile', ...VIEWPORT_CONFIG.mobile };
    if (width <= VIEWPORT_CONFIG.tablet.width) return { type: 'tablet', ...VIEWPORT_CONFIG.tablet };
    return { type: 'desktop', ...VIEWPORT_CONFIG.desktop };
}