/**
 * Main Popup Controller
 * Orchestrates all modular components while preserving 100% functionality
 */

import { GOOGLE_GEMINI_VOICES } from './shared/google-gemini-voices.js';
import { DOM } from './modules/dom-elements.js';
import { StorageManager } from './modules/storage-manager.js';
import { ElevenLabsProvider } from './modules/elevenlabs-provider.js';
import { HumeProvider } from './modules/hume-provider.js';
import { StraicoProvider } from './modules/straico-provider.js';
import { GoogleProvider } from './modules/google-provider.js';
import { UIControls } from './modules/ui-controls.js';
import { TabManager } from './modules/tab-manager.js';
import { TextToAudio } from './modules/text-to-audio.js';

class PopupController {
    constructor() {
        this.providers = {};
        this.currentProvider = 'google';
        this.statusDiv = null;
    }

    async initialize() {
        // Initialize all modules
        this.elevenLabsProvider = new ElevenLabsProvider(DOM);
        this.humeProvider = new HumeProvider(DOM);
        this.straicoProvider = new StraicoProvider(DOM);
        this.googleProvider = new GoogleProvider(DOM);
        this.uiControls = new UIControls(DOM);
        this.tabManager = new TabManager(DOM);
        this.textToAudio = new TextToAudio(DOM);

        // Store providers for easy access
        this.providers = {
            elevenlabs: this.elevenLabsProvider,
            hume: this.humeProvider,
            straico: this.straicoProvider,
            google: this.googleProvider
        };

        // Set up provider utility methods
        this.setupProviderUtilities();

        // Initialize all modules
        this.elevenLabsProvider.initialize();
        this.humeProvider.initialize();
        this.straicoProvider.initialize();
        this.googleProvider.initialize();
        this.uiControls.initialize();
        this.tabManager.initialize();
        this.textToAudio.initialize();

        // Set up provider selection handler
        this.setupProviderSelection();

        // Set up TTA info callback
        this.textToAudio.setUpdateTtaInfoCallback(() => this.updateTtaInfo());

        // Load and restore all settings
        await this.loadAndRestoreSettings();
    }

    setupProviderUtilities() {
        // Provide shared utility methods to all providers
        const updateStatusMethod = (message, color) => this.updateStatus(message, color);

        this.elevenLabsProvider.updateStatus = updateStatusMethod;
        this.humeProvider.updateStatus = updateStatusMethod;
        this.straicoProvider.updateStatus = updateStatusMethod;
    }

    setupProviderSelection() {
        DOM.providerSelect.addEventListener('change', async () => {
            const provider = DOM.providerSelect.value;
            this.currentProvider = provider;

            await StorageManager.saveSync({ selectedProvider: provider });

            this.toggleProviderSettings();
            this.updateTtaInfo();
            this.displayUsageInfo();

            // Notify content script about provider change
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        type: 'provider-changed',
                        provider: provider
                    });
                }
            });
        });
    }

    async loadAndRestoreSettings() {
        if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
            const { sync: syncData, local: localData } = await StorageManager.loadAll();

            // Set provider first, as other settings depend on it
            this.currentProvider = syncData.selectedProvider || 'google';
            DOM.providerSelect.value = this.currentProvider;

            // Save default provider if none was set
            if (!syncData.selectedProvider) {
                await StorageManager.saveSync({ selectedProvider: this.currentProvider });
            }

            // Restore settings for all providers
            await this.elevenLabsProvider.restoreSettings(syncData, localData);
            await this.humeProvider.restoreSettings(syncData, localData);
            await this.straicoProvider.restoreSettings(syncData, localData);
            await this.googleProvider.restoreSettings(syncData, localData);
            await this.uiControls.restoreSettings(syncData, localData);
            this.textToAudio.restoreSettings(localData);

            // Update all UI sections
            this.toggleProviderSettings();
            this.updateTtaInfo();

            // Restore the last active tab
            this.tabManager.restoreLastActiveTab(syncData);

            // Display usage info
            this.displayUsageInfo();
        }
    }

    toggleProviderSettings() {
        // Hide all provider settings first
        Object.values(this.providers).forEach(provider => {
            provider.hide();
        });

        // Show current provider settings
        if (this.providers[this.currentProvider]) {
            this.providers[this.currentProvider].show();
        }
    }

    updateTtaInfo() {
        const provider = this.currentProvider;
        DOM.ttaProviderSpan.textContent = `Provider: ${provider.charAt(0).toUpperCase() + provider.slice(1)}`;
        DOM.ttaModelSpan.style.visibility = 'hidden';
        DOM.ttaCreditsSpan.style.visibility = 'visible';
        DOM.ttaVoiceSpan.style.display = 'block';
        this.textToAudio.hideOutputFormat();

        // Update provider-specific info
        if (this.providers[provider] && this.providers[provider].updateTtaInfo) {
            this.providers[provider].updateTtaInfo();
        }
    }

    async displayUsageInfo() {
        if (this.currentProvider === 'google') {
            await this.googleProvider.displayUsageInfo();
        }
    }

    updateStatus(message, color) {
        if (this.statusDiv) {
            this.statusDiv.textContent = message;
            this.statusDiv.style.color = color;
            setTimeout(() => { this.statusDiv.textContent = ''; }, 4000);
        } else {
            console.log(`Status (${color}): ${message}`);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const popupController = new PopupController();
    await popupController.initialize();
});

// Handle clipboard requests (for backward compatibility)
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'get-clipboard-text') {
        const textarea = document.getElementById('clipboard-helper');
        textarea.focus();
        document.execCommand('paste');
        const text = textarea.value;
        chrome.runtime.sendMessage({ type: 'clipboard-text-response', text: text });
    }
});