/**
 * Hume AI Provider Module
 * Handles all Hume AI-specific functionality including voice fetching and settings
 */

import { StorageManager } from './storage-manager.js';

export class HumeProvider {
    constructor(domElements) {
        this.dom = domElements;
        this.allVoices = [];
    }

    /**
     * Initialize Hume provider with event listeners
     */
    initialize() {
        // API key auto-save handler
        this.dom.humeApiKeyInput.addEventListener('input',
            StorageManager.createAutoSaveHandler(
                this.dom.humeApiKeyInput,
                'humeApiKey',
                () => {
                    this.fetchAndStoreVoices();
                    this.updateTtaInfo();
                }
            )
        );

        // Voice selection handler
        this.dom.humeVoiceSelect.addEventListener('change', () => {
            const selectedOption = this.dom.humeVoiceSelect.options[this.dom.humeVoiceSelect.selectedIndex];
            StorageManager.saveSync({
                selectedHumeVoiceId: selectedOption.value,
                selectedHumeVoiceName: selectedOption.dataset.name
            });
            this.updateTtaInfo();
        });

        // Speed slider handler
        this.dom.humeSpeedSlider.addEventListener('input', () => {
            const speed = parseFloat(this.dom.humeSpeedSlider.value);
            this.dom.humeSpeedValue.textContent = speed.toFixed(2);
            StorageManager.saveSync({ humePlaybackSpeed: speed });
        });
    }

    /**
     * Fetch and store voices from Hume AI API
     */
    async fetchAndStoreVoices() {
        this.dom.humeVoiceStatus.textContent = 'Loading...';
        const apiKey = this.dom.humeApiKeyInput.value.trim();

        if (!apiKey) {
            this.updateStatus('Please enter a Hume API key first.', 'red');
            return;
        }

        try {
            const response = await fetch('https://api.hume.ai/v0/tts/voices?provider=HUME_AI', {
                headers: { 'X-Hume-Api-Key': apiKey }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Hume API Error: ${errorData?.message || response.statusText}`);
            }

            const data = await response.json();
            const voices = data.voices_page || [];

            await StorageManager.saveLocal({ allHumeVoices: voices });

            const { sync: syncData } = await StorageManager.loadAll();
            let selectedVoiceId = syncData.selectedHumeVoiceId;
            const isSelectedVoiceInList = voices.some(v => v.id === selectedVoiceId);

            if (!isSelectedVoiceInList && voices.length > 0) {
                selectedVoiceId = voices[0].id;
                await StorageManager.saveSync({
                    selectedHumeVoiceId: selectedVoiceId,
                    selectedHumeVoiceName: voices[0].name
                });
            }

            this.populateVoiceSelect(voices, selectedVoiceId);
            this.dom.humeVoiceStatus.textContent = `Loaded ${voices.length} Hume voices!`;
        } catch (error) {
            this.dom.humeVoiceStatus.textContent = error.message;
        }
    }

    /**
     * Populate Hume voice selection dropdown
     */
    populateVoiceSelect(voices, selectedVoiceId) {
        this.dom.humeVoiceSelect.innerHTML = '';

        if (!voices || voices.length === 0) {
            this.dom.humeVoiceSelect.innerHTML = '<option>No voices found</option>';
            return;
        }

        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.id;
            option.dataset.name = voice.name;
            option.textContent = `${voice.name} (${voice.provider})`;
            if (voice.id === selectedVoiceId) option.selected = true;
            this.dom.humeVoiceSelect.appendChild(option);
        });
    }

    /**
     * Update Text-to-Audio info display
     */
    updateTtaInfo() {
        this.dom.ttaCreditsSpan.style.visibility = 'hidden';
        const selectedVoice = this.dom.humeVoiceSelect.options[this.dom.humeVoiceSelect.selectedIndex]?.text;
        this.dom.ttaVoiceSpan.textContent = `Voice: ${selectedVoice || 'N/A'}`;
    }

    /**
     * Restore settings from storage
     */
    async restoreSettings(syncData, localData) {
        // Restore API key
        if (syncData.humeApiKey) {
            this.dom.humeApiKeyInput.value = syncData.humeApiKey;
        }

        // Restore speed slider
        const initialHumeSpeed = syncData.humePlaybackSpeed || 1.0;
        this.dom.humeSpeedSlider.value = initialHumeSpeed;
        this.dom.humeSpeedValue.textContent = parseFloat(initialHumeSpeed).toFixed(2);

        // Populate voice lists from cache
        if (localData.allHumeVoices && localData.allHumeVoices.length > 0) {
            let selectedVoiceId = syncData.selectedHumeVoiceId;
            const voices = localData.allHumeVoices;
            const isSelectedVoiceInList = voices.some(v => v.id === selectedVoiceId);

            if (!isSelectedVoiceInList && voices.length > 0) {
                selectedVoiceId = voices[0].id;
                StorageManager.saveSync({
                    selectedHumeVoiceId: selectedVoiceId,
                    selectedHumeVoiceName: voices[0].name
                });
            }

            this.populateVoiceSelect(voices, selectedVoiceId);
        }
    }

    /**
     * Show provider-specific UI elements
     */
    show() {
        this.dom.humeSettings.style.display = 'block';
        this.dom.humeVoiceSettings.style.display = 'block';
    }

    /**
     * Hide provider-specific UI elements
     */
    hide() {
        this.dom.humeSettings.style.display = 'none';
        this.dom.humeVoiceSettings.style.display = 'none';
    }

    // Utility method
    updateStatus(message, color) {
        console.log(`${color}: ${message}`);
    }
}