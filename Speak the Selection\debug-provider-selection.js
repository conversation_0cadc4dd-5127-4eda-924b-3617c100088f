/**
 * Debug script to check provider selection and settings
 * Run this in the browser console to diagnose provider selection issues
 */

console.log('=== Provider Selection Debug ===');

// Check current storage settings
chrome.storage.sync.get([
    'selectedProvider', 
    'elevenLabsApiKey', 
    'humeApiKey', 
    'straicoApiKey', 
    'googleApiKey',
    'selectedVoiceId',
    'selectedHumeVoiceId',
    'selectedHumeVoiceName',
    'selectedStraicoVoiceId',
    'selectedGoogleVoice'
], (result) => {
    console.log('=== Current Storage Settings ===');
    console.log('Selected Provider:', result.selectedProvider);
    console.log('Google API Key:', result.googleApiKey ? '***SET***' : 'NOT SET');
    console.log('ElevenLabs API Key:', result.elevenLabsApiKey ? '***SET***' : 'NOT SET');
    console.log('Hume API Key:', result.humeApiKey ? '***SET***' : 'NOT SET');
    console.log('Straico API Key:', result.straicoApiKey ? '***SET***' : 'NOT SET');
    console.log('Selected Voice ID (ElevenLabs):', result.selectedVoiceId);
    console.log('Selected Hume Voice ID:', result.selectedHumeVoiceId);
    console.log('Selected Hume Voice Name:', result.selectedHumeVoiceName);
    console.log('Selected Straico Voice ID:', result.selectedStraicoVoiceId);
    console.log('Selected Google Voice:', result.selectedGoogleVoice);
    
    console.log('\n=== Validation Results ===');
    
    // Simulate the validation logic
    const providerName = result.selectedProvider;
    if (!providerName) {
        console.log('❌ ERROR: No TTS provider selected. Please select a provider in the extension popup.');
        return;
    }
    
    console.log('✓ Provider selected:', providerName);
    
    // Check provider-specific requirements
    switch (providerName) {
        case 'google':
            if (!result.googleApiKey) {
                console.log('❌ ERROR: Google API key is required. Please click the "🔑 Get API key" link in the extension popup to get your free Google API key.');
            } else {
                console.log('✓ Google API key is set');
            }
            break;
            
        case 'elevenlabs':
            if (!result.elevenLabsApiKey) {
                console.log('❌ ERROR: ElevenLabs API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your ElevenLabs API key.');
            } else {
                console.log('✓ ElevenLabs API key is set');
            }
            if (!result.selectedVoiceId) {
                console.log('❌ ERROR: ElevenLabs voice is required. Please select a voice in the extension popup.');
            } else {
                console.log('✓ ElevenLabs voice is selected');
            }
            break;
            
        case 'hume':
            if (!result.humeApiKey) {
                console.log('❌ ERROR: Hume API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your Hume API key.');
            } else {
                console.log('✓ Hume API key is set');
            }
            if (!result.selectedHumeVoiceId || !result.selectedHumeVoiceName) {
                console.log('❌ ERROR: Hume voice is required. Please select a voice in the extension popup.');
            } else {
                console.log('✓ Hume voice is selected');
            }
            break;
            
        case 'straico':
            if (!result.straicoApiKey) {
                console.log('❌ ERROR: Straico API key is required. Please click the "🔑 Get free API key" link in the extension popup to get your Straico API key.');
            } else {
                console.log('✓ Straico API key is set');
            }
            if (!result.selectedStraicoVoiceId) {
                console.log('❌ ERROR: Straico voice is required. Please select a voice in the extension popup.');
            } else {
                console.log('✓ Straico voice is selected');
            }
            break;
            
        default:
            console.log('❌ ERROR: Unknown provider:', providerName);
    }
});

// Helper function to set a default provider for testing
window.setDefaultProvider = async function(provider = 'google') {
    await chrome.storage.sync.set({ selectedProvider: provider });
    console.log('Set default provider to:', provider);
    console.log('Please refresh the page and try again');
};

// Helper function to clear all settings for testing
window.clearAllSettings = async function() {
    await chrome.storage.sync.clear();
    await chrome.storage.local.clear();
    console.log('All settings cleared. Please refresh the page and reconfigure.');
};

console.log('\n=== Test Commands ===');
console.log('Run setDefaultProvider("google") to set Google as default provider');
console.log('Run clearAllSettings() to reset all settings');
console.log('Then try selecting text and clicking play button');
