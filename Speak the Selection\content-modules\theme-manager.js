/**
 * Theme Manager Module
 * Handles theme detection, application, and management for the TTS extension
 */

export class ThemeManager {
    constructor() {
        this.currentTheme = 'auto';
        this.currentRange = null;
        this.playerContainer = null;
    }

    /**
     * Initialize theme manager
     */
    initialize(currentTheme = 'auto') {
        this.currentTheme = currentTheme;
    }

    /**
     * Set current range for theme detection
     */
    setCurrentRange(range) {
        this.currentRange = range;
    }

    /**
     * Set player container reference
     */
    setPlayerContainer(container) {
        this.playerContainer = container;
    }

    /**
     * Update theme setting
     */
    setTheme(theme) {
        this.currentTheme = theme;
        if (this.playerContainer && this.currentRange) {
            const detectedTheme = this.detectCurrentTheme(this.currentRange);
            this.applyThemeToPlayer(detectedTheme);
        }
    }

    /**
     * Calculate luminance for theme detection
     */
    getLuminance(rgb) {
        const [r, g, b] = rgb.map(v => {
            v /= 255;
            return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    /**
     * Detect current theme based on range context or user preference
     */
    detectCurrentTheme(range) {
        // Use user-selected theme preference
        if (this.currentTheme === 'light') {
            return 'light';
        } else if (this.currentTheme === 'dark') {
            return 'dark';
        }

        // Auto mode: detect based on page or system preference
        if (range) {
            let node = range.commonAncestorContainer;
            if (node.nodeType !== Node.ELEMENT_NODE) node = node.parentNode;
            while (node && node !== document.body) {
                const style = window.getComputedStyle(node);
                const bgColor = style.backgroundColor;
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    const rgb = bgColor.match(/\d+/g).map(Number);
                    return this.getLuminance(rgb) > 0.5 ? 'light' : 'dark';
                }
                node = node.parentNode;
            }
        }

        // Fallback to system preference
        return window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
    }

    /**
     * Apply theme to player container
     */
    applyThemeToPlayer(theme) {
        if (!this.playerContainer) return;

        // Set theme attribute
        this.playerContainer.setAttribute('data-theme', theme);

        // Apply theme-specific emergency styling
        const isLight = theme === 'light';

        // Force text visibility with theme-appropriate colors
        const textElements = this.playerContainer.querySelectorAll('.tts-volume-value, .tts-speed-value, .tts-provider-label, .tts-time-label, label');
        textElements.forEach(element => {
            if (isLight) {
                element.style.cssText += `
                    color: #0f172a !important;
                    font-weight: 700 !important;
                    text-shadow: none !important;
                `;
            } else {
                element.style.cssText += `
                    color: #e5e7eb !important;
                    font-weight: 500 !important;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
                `;
            }
        });

        // Update container background for better contrast
        const containerStyle = isLight ? `
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        ` : `
            background: rgba(22, 28, 44, 0.95) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        `;

        this.playerContainer.style.cssText += containerStyle;
    }

    /**
     * Auto-position and apply theme to player
     */
    autoPositionAndTheme() {
        if (!this.currentRange) return;

        const theme = this.detectCurrentTheme(this.currentRange);
        this.applyThemeToPlayer(theme);
        return theme;
    }

    /**
     * Handle theme change messages
     */
    handleThemeChange(newTheme, shadowHost) {
        this.currentTheme = newTheme || 'auto';

        // Re-apply theme to existing player if visible
        if (this.playerContainer && shadowHost && shadowHost.style.display !== 'none') {
            const theme = this.detectCurrentTheme(this.currentRange);
            this.applyThemeToPlayer(theme);
        }
    }

    /**
     * Get current theme setting
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Apply theme to progress overlay
     */
    applyThemeToProgressOverlay(overlay, range = null) {
        const themeRange = range || this.currentRange;
        const theme = this.detectCurrentTheme(themeRange);

        if (theme === 'light') {
            overlay.classList.add('light-theme');
        } else {
            overlay.classList.remove('light-theme');
        }

        return theme;
    }

    /**
     * Apply theme to simple loader
     */
    getLoaderThemeStyles(range = null) {
        const themeRange = range || this.currentRange;
        const theme = this.detectCurrentTheme(themeRange);
        const isLight = theme === 'light';

        return {
            theme,
            isLight,
            loaderBg: isLight ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.5)',
            loaderBorder: isLight ? '#2563eb' : '#fff',
            shadowColor: isLight ? 'rgba(37, 99, 235, 0.4)' : 'rgba(255, 255, 255, 0.4)'
        };
    }
}