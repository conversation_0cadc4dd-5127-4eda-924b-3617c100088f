<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Theme Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }

        .theme-demo {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }

        .light-theme {
            background: #ffffff;
            color: #333;
        }

        .dark-theme {
            background: #1a1a1a;
            color: #e0e0e0;
        }

        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #2563eb;
            margin: 20px 0;
        }

        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .highlight {
            background: #ffec8a;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>

<body>
    <h1>🎨 TTS Extension Theme Test Page</h1>

    <div class="instructions">
        <h2>📋 How to Test Theme Switching:</h2>
        <ol>
            <li><strong>Load the extension</strong> in Chrome (reload if already loaded)</li>
            <li><strong>Open the extension popup</strong> by clicking the extension icon</li>
            <li><strong>Go to Settings tab</strong> and find the new "Theme" dropdown</li>
            <li><strong>Try different themes:</strong> Auto (System), Light, Dark</li>
            <li><strong>Select text below</strong> and test the floating player appearance</li>
        </ol>
    </div>

    <div class="feature-list">
        <h2>✨ New Theme Features Added:</h2>
        <ul>
            <li>🎨 <strong>User-controlled theme switcher</strong> in popup settings</li>
            <li>🌓 <strong>Three theme options:</strong> Auto (system), Light, Dark</li>
            <li>💾 <strong>Persistent theme storage</strong> - remembers your choice</li>
            <li>⚡ <strong>Real-time theme switching</strong> - no reload needed</li>
            <li>👁️ <strong>Enhanced visibility</strong> in both light and dark themes</li>
            <li>🔄 <strong>Smooth transitions</strong> between theme changes</li>
        </ul>
    </div>

    <h2>Test Text Selection (Light Theme Background)</h2>
    <div class="theme-demo light-theme">
        <p>This is sample text on a <span class="highlight">light background</span>. Select this text to see how the TTS
            player appears with the light theme. The player should have dark text on a light background for good
            contrast and readability.</p>

        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore
            magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
    </div>

    <h2>Test Text Selection (Dark Theme Background)</h2>
    <div class="theme-demo dark-theme">
        <p>This is sample text on a <span style="background: #444; color: #fff; padding: 2px 4px;">dark
                background</span>. Select this text to see how the TTS player appears with the dark theme. The player
            should have light text on a dark background for optimal visibility.</p>

        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
            Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit.</p>
    </div>

    <h2>🧪 Testing Instructions:</h2>
    <div class="instructions">
        <h3>Theme Auto-Detection Test:</h3>
        <ol>
            <li>Set theme to "Auto" in popup settings</li>
            <li>Select text in the light theme section above</li>
            <li>Player should appear with dark text (light theme detected)</li>
            <li>Select text in the dark theme section above</li>
            <li>Player should appear with light text (dark theme detected)</li>
        </ol>

        <h3>Manual Theme Override Test:</h3>
        <ol>
            <li>Set theme to "Light" in popup settings</li>
            <li>Select any text - player should always use light theme</li>
            <li>Set theme to "Dark" in popup settings</li>
            <li>Select any text - player should always use dark theme</li>
        </ol>
    </div>

    <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
        <h2>✅ Expected Results:</h2>
        <ul>
            <li><strong>Light Theme:</strong> Dark text, white/light background, blue accent</li>
            <li><strong>Dark Theme:</strong> Light text, dark background, blue accent with glow</li>
            <li><strong>Auto Mode:</strong> Automatically switches based on page background</li>
            <li><strong>Persistent:</strong> Theme choice is remembered across sessions</li>
            <li><strong>Real-time:</strong> Changes apply immediately without reload</li>
        </ul>
    </div>
</body>

</html>