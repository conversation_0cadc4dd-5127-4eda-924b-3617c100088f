// Enhanced Storage Manager for TTS Extension
// Provides batched storage operations for better performance
// Maintains 100% backward compatibility with existing chrome.storage usage

class StorageManager {
    static pendingWrites = new Map();
    static batchTimeout = null;
    static readonly = false; // For testing/readonly mode

    // Batch storage writes for better performance
    static async batchWrite(key, value, storage = 'sync') {
        if (this.readonly) return; // Safety for testing

        this.pendingWrites.set(key, { value, storage });

        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
        }

        this.batchTimeout = setTimeout(() => this.flushWrites(), 100);
    }

    // Flush all pending writes
    static async flushWrites() {
        if (this.pendingWrites.size === 0) return;

        const syncData = {};
        const localData = {};

        for (const [key, { value, storage }] of this.pendingWrites) {
            if (storage === 'sync') {
                syncData[key] = value;
            } else {
                localData[key] = value;
            }
        }

        try {
            const promises = [];

            if (Object.keys(syncData).length > 0) {
                promises.push(chrome.storage.sync.set(syncData));
            }

            if (Object.keys(localData).length > 0) {
                promises.push(chrome.storage.local.set(localData));
            }

            await Promise.all(promises);

        } catch (error) {
            console.warn('Storage batch write failed:', error);
            // Fallback to individual writes
            for (const [key, { value, storage }] of this.pendingWrites) {
                try {
                    if (storage === 'sync') {
                        await chrome.storage.sync.set({ [key]: value });
                    } else {
                        await chrome.storage.local.set({ [key]: value });
                    }
                } catch (fallbackError) {
                    console.warn(`Failed to write ${key}:`, fallbackError);
                }
            }
        } finally {
            this.pendingWrites.clear();
            this.batchTimeout = null;
        }
    }

    // Enhanced get with caching
    static cachedReads = new Map();
    static cacheTimeout = 5000; // 5 second cache

    static async get(keys, storage = 'sync') {
        const cacheKey = `${storage}-${JSON.stringify(keys)}`;
        const cached = this.cachedReads.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }

        try {
            let data;
            if (storage === 'sync') {
                data = await chrome.storage.sync.get(keys);
            } else {
                data = await chrome.storage.local.get(keys);
            }

            // Cache the result
            this.cachedReads.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            // Clean old cache entries periodically
            if (this.cachedReads.size > 50) {
                this.cleanCache();
            }

            return data;

        } catch (error) {
            console.warn('Storage read failed:', error);
            return Array.isArray(keys) ? {} : { [keys]: undefined };
        }
    }

    // Clean old cache entries
    static cleanCache() {
        const now = Date.now();
        for (const [key, { timestamp }] of this.cachedReads) {
            if (now - timestamp > this.cacheTimeout) {
                this.cachedReads.delete(key);
            }
        }
    }

    // Immediate write (bypass batching for critical data)
    static async immediateWrite(key, value, storage = 'sync') {
        try {
            if (storage === 'sync') {
                await chrome.storage.sync.set({ [key]: value });
            } else {
                await chrome.storage.local.set({ [key]: value });
            }
        } catch (error) {
            console.warn(`Immediate write failed for ${key}:`, error);
        }
    }

    // Clear all caches
    static clearCache() {
        this.cachedReads.clear();
    }

    // Force flush (for cleanup)
    static async forceFlush() {
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }
        await this.flushWrites();
    }
}

// Backward compatibility wrapper - drop-in replacement for chrome.storage
export const optimizedStorage = {
    sync: {
        get: (keys) => StorageManager.get(keys, 'sync'),
        set: (data) => {
            // Handle both single and batch sets
            for (const [key, value] of Object.entries(data)) {
                StorageManager.batchWrite(key, value, 'sync');
            }
        },
        setImmediate: (data) => {
            for (const [key, value] of Object.entries(data)) {
                StorageManager.immediateWrite(key, value, 'sync');
            }
        }
    },
    local: {
        get: (keys) => StorageManager.get(keys, 'local'),
        set: (data) => {
            for (const [key, value] of Object.entries(data)) {
                StorageManager.batchWrite(key, value, 'local');
            }
        },
        setImmediate: (data) => {
            for (const [key, value] of Object.entries(data)) {
                StorageManager.immediateWrite(key, value, 'local');
            }
        }
    }
};

export { StorageManager };