/**
 * Straico Provider Module
 * Handles all Straico-specific functionality including voice fetching, credits, and model selection
 */

import { StorageManager } from './storage-manager.js';

export class StraicoProvider {
    constructor(domElements) {
        this.dom = domElements;
        this.allVoices = [];
    }

    /**
     * Initialize Straico provider with event listeners
     */
    initialize() {
        // API key auto-save handler
        this.dom.straicoApiKeyInput.addEventListener('input',
            StorageManager.createAutoSaveHandler(
                this.dom.straicoApiKeyInput,
                'straicoApiKey',
                (apiKey) => {
                    this.fetchAndDisplayCredits(apiKey);
                    this.fetchAndStoreVoices();
                    this.updateTtaInfo();
                }
            )
        );

        // Model selection handler
        this.dom.straicoModelSelect.addEventListener('change', () => {
            StorageManager.saveSync({ selectedStraicoModel: this.dom.straicoModelSelect.value }).then(() => {
                this.fetchAndStoreVoices();
                this.updateTtaInfo();
            });
        });

        // Voice selection handler
        this.dom.straicoVoiceSelect.addEventListener('change', () => {
            StorageManager.saveSync({ selectedStraicoVoiceId: this.dom.straicoVoiceSelect.value });
            this.updateTtaInfo();
        });

        // Speed slider handler
        this.dom.straicoSpeedSlider.addEventListener('input', () => {
            const speed = parseFloat(this.dom.straicoSpeedSlider.value);
            this.dom.straicoSpeedValue.textContent = speed.toFixed(2);
            StorageManager.saveSync({ straicoPlaybackSpeed: speed });
        });
    }

    /**
     * Fetch and display Straico credits
     * @param {string} apiKey - Straico API key
     */
    async fetchAndDisplayCredits(apiKey) {
        if (!apiKey) {
            this.dom.straicoCreditsDiv.textContent = '';
            return;
        }

        this.dom.straicoCreditsDiv.textContent = 'Loading coins...';

        try {
            const response = await fetch('https://api.straico.com/v0/user', {
                headers: { 'Authorization': `Bearer ${apiKey}` }
            });

            if (!response.ok) throw new Error('Failed to fetch credits.');

            const data = await response.json();
            if (data.success) {
                this.dom.straicoCreditsDiv.textContent = `Coins: ${data.data.coins.toFixed(2)}`;
                this.updateTtaInfo();
            } else {
                throw new Error(data.message || 'Failed to fetch credits.');
            }
        } catch (error) {
            this.dom.straicoCreditsDiv.textContent = 'Could not load credits.';
        }
    }

    /**
     * Fetch and store voices from Straico API
     */
    async fetchAndStoreVoices() {
        this.dom.straicoVoiceStatus.textContent = 'Loading...';
        const apiKey = this.dom.straicoApiKeyInput.value.trim();

        if (!apiKey) {
            this.updateStatus('Please enter a Straico API key first.', 'red');
            return;
        }

        const model = this.dom.straicoModelSelect.value;
        let voices = [];

        if (model === 'eleven_multilingual_v2') {
            try {
                const response = await fetch('https://api.straico.com/v1/tts/elevenlabslist', {
                    headers: { 'Authorization': `Bearer ${apiKey}` }
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`Straico API Error: ${errorData?.message || response.statusText}`);
                }

                const voiceData = await response.json();
                voices = (voiceData?.data?.voices && Array.isArray(voiceData.data.voices))
                    ? voiceData.data.voices : [];
            } catch (error) {
                this.dom.straicoVoiceStatus.textContent = error.message;
                return;
            }
        } else if (model === 'tts-1') {
            voices = [
                { voice_id: 'alloy', name: 'Alloy' },
                { voice_id: 'echo', name: 'Echo' },
                { voice_id: 'fable', name: 'Fable' },
                { voice_id: 'onyx', name: 'Onyx' },
                { voice_id: 'nova', name: 'Nova' },
                { voice_id: 'shimmer', name: 'Shimmer' }
            ];
        }

        await StorageManager.saveLocal({ allStraicoVoices: voices });

        const { sync: syncData } = await StorageManager.loadAll();
        const currentVoiceId = syncData.selectedStraicoVoiceId;
        const isCurrentVoiceValid = voices.some(v => v.voice_id === currentVoiceId);
        let selectedVoiceId = isCurrentVoiceValid ? currentVoiceId : (voices[0]?.voice_id || null);

        if (!isCurrentVoiceValid && selectedVoiceId) {
            await StorageManager.saveSync({ selectedStraicoVoiceId: selectedVoiceId });
        }

        this.populateVoiceSelect(voices, selectedVoiceId);
        this.dom.straicoVoiceStatus.textContent = voices.length > 0
            ? `Loaded ${voices.length} Straico voices!`
            : 'No voices found for this model.';
    }

    /**
     * Populate Straico voice selection dropdown
     */
    populateVoiceSelect(voices, selectedVoiceId) {
        this.dom.straicoVoiceSelect.innerHTML = '';

        if (!Array.isArray(voices) || voices.length === 0) {
            this.dom.straicoVoiceSelect.innerHTML = '<option>No voices found</option>';
            return;
        }

        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.voice_id;
            const { accent, description, age, use_case } = voice.labels || {};
            const details = [accent, description, age, use_case].filter(Boolean).join(', ');
            option.textContent = details ? `${voice.name} (${details})` : voice.name;
            if (voice.voice_id === selectedVoiceId) option.selected = true;
            this.dom.straicoVoiceSelect.appendChild(option);
        });
    }

    /**
     * Update Text-to-Audio info display
     */
    updateTtaInfo() {
        this.dom.ttaCreditsSpan.textContent = this.dom.straicoCreditsDiv.textContent || 'Credits: Not available';
        this.dom.ttaCreditsSpan.style.cssText = 'color: var(--muted);';
        this.dom.ttaModelSpan.style.visibility = 'visible';
        const selectedModel = this.dom.straicoModelSelect.options[this.dom.straicoModelSelect.selectedIndex].text;
        this.dom.ttaModelSpan.textContent = `Model: ${selectedModel}`;
        const selectedVoice = this.dom.straicoVoiceSelect.options[this.dom.straicoVoiceSelect.selectedIndex]?.text;
        this.dom.ttaVoiceSpan.textContent = `Voice: ${selectedVoice || 'N/A'}`;
    }

    /**
     * Restore settings from storage
     */
    async restoreSettings(syncData, localData) {
        // Restore API key
        if (syncData.straicoApiKey) {
            this.dom.straicoApiKeyInput.value = syncData.straicoApiKey;
        }

        // Restore model selection
        if (syncData.selectedStraicoModel) {
            this.dom.straicoModelSelect.value = syncData.selectedStraicoModel;
        }

        // Restore speed slider
        const initialStraicoSpeed = syncData.straicoPlaybackSpeed || 1.0;
        this.dom.straicoSpeedSlider.value = initialStraicoSpeed;
        this.dom.straicoSpeedValue.textContent = parseFloat(initialStraicoSpeed).toFixed(2);

        // Populate voice lists from cache
        if (localData.allStraicoVoices && localData.allStraicoVoices.length > 0) {
            this.populateVoiceSelect(localData.allStraicoVoices, syncData.selectedStraicoVoiceId);
        }

        // Fetch fresh credit info
        if (syncData.straicoApiKey) {
            this.fetchAndDisplayCredits(syncData.straicoApiKey);
        }
    }

    /**
     * Show provider-specific UI elements
     */
    show() {
        this.dom.straicoSettings.style.display = 'block';
        this.dom.straicoVoiceSettings.style.display = 'block';
    }

    /**
     * Hide provider-specific UI elements
     */
    hide() {
        this.dom.straicoSettings.style.display = 'none';
        this.dom.straicoVoiceSettings.style.display = 'none';
    }

    // Utility method
    updateStatus(message, color) {
        console.log(`${color}: ${message}`);
    }
}