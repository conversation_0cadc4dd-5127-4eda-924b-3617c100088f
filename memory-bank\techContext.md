# Technical Context: TTS Chrome Extension

## Technologies Used
- **HTML5:** Used for the structure of the popup and offscreen documents.
- **CSS3:** Used for styling the popup interface and the floating play button. All player-related styles are encapsulated in a dedicated `player.css` file.
- **JavaScript (ES6+):** The core logic of the extension is written in modern JavaScript.
- **Chrome Extension APIs:** The extension utilizes various Chrome Extension APIs, including:
    - `chrome.runtime`: For managing the extension's lifecycle and communication, including persistent port connections.
    - `chrome.storage`: For storing user settings.
    - `chrome.contextMenus`: For adding the "Read aloud" option to the context menu.
    - `chrome.offscreen`: For playing audio in the background.
    - `chrome.notifications`: For displaying notifications to the user.
    - `chrome.tabs`: For interacting with browser tabs.
- **TTS APIs:** The extension integrates with multiple TTS providers:
    - **ElevenLabs API**
    - **Hume AI API**
    - **Straico API**

## Development Setup
The extension can be loaded into Chrome or any Chromium-based browser in developer mode. No special build process is required.

## Dependencies
The extension has no external JavaScript libraries or frameworks as dependencies. It relies solely on the built-in browser APIs and the APIs of the supported TTS providers (ElevenLabs, Hume AI, Straico).
