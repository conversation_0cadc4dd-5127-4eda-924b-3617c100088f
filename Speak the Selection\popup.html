<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>TTS Settings</title>
    <link rel="stylesheet" href="styles/popup-base.css">
    <link rel="stylesheet" href="styles/popup-themes.css">
    <link rel="stylesheet" href="styles/popup-tabs.css">
    <link rel="stylesheet" href="styles/popup-components.css">


</head>

<body>
    <div class="tab-nav">
        <button class="tab-button active" data-tab="settings">Settings</button>
        <button class="tab-button" data-tab="text-to-audio">Text to Audio</button>
    </div>

    <div id="settings" class="tab-content active">
        <h1>API Settings</h1>
        <div class="input-group">
            <label for="provider-select">TTS Provider:</label>
            <select id="provider-select">
                <option value="google">Google (multilingual)</option>
                <option value="elevenlabs">ElevenLabs</option>
                <option value="hume">Hume AI</option>
                <option value="straico">Straico</option>
            </select>
        </div>

        <!-- ElevenLabs Settings -->
        <div id="elevenlabs-settings" class="provider-settings">
            <label for="api-key">ElevenLabs API Key:</label>
            <input type="password" id="api-key" />
            <div class="api-key-link">
                <a href="https://try.elevenlabs.io/2tbh46hnf6or" target="_blank">🔑 Get free API key</a>
            </div>
            <div id="elevenlabs-credits" style="margin-top: 4px; margin-bottom: 8px; font-size: 12px;"></div>
        </div>

        <!-- Hume AI Settings -->
        <div id="hume-settings" class="provider-settings" style="display: none;">
            <label for="hume-api-key">Hume API Key:</label>
            <input type="password" id="hume-api-key" />
            <div class="api-key-link">
                <a href="https://try.hume.ai/rfvvke3p4kla" target="_blank">🔑 Get free API key</a>
            </div>
        </div>

        <!-- Straico Settings -->
        <div id="straico-settings" class="provider-settings" style="display: none;">
            <label for="straico-api-key">Straico API Key:</label>
            <input type="password" id="straico-api-key" />
            <div class="api-key-link">
                <a href="https://softreviewed.com/straico-discount" target="_blank">🔑 Get free API key</a>
            </div>
            <div id="straico-credits" style="margin-top: 4px; margin-bottom: 8px; font-size: 12px;"></div>
        </div>

        <div id="google-settings" class="provider-settings" style="display: none;">
            <label for="google-api-key">Google API Key:</label>
            <input type="password" id="google-api-key" />
            <div class="api-key-link">
                <a href="https://aistudio.google.com/app/apikey" target="_blank">🔑 Get API key</a>
            </div>

            <div id="google-requests-container" style="font-size: 12px; margin-bottom: 8px; color: var(--muted);">
            </div>

            <label for="google-voice">Select a Voice:</label>
            <select id="google-voice"></select>
        </div>

        <div id="general-settings-wrapper">
            <h2>General Settings</h2>
            <div class="toggle-button" id="show-floating-button-toggle" data-setting="show-floating-button">
                <div class="toggle-indicator"></div>
                <span class="toggle-label">Show floating play button on text selection</span>
            </div>
            <div class="toggle-button" id="enable-highlighting-toggle" data-setting="enable-highlighting">
                <div class="toggle-indicator"></div>
                <span class="toggle-label">Enable word highlighting</span>
            </div>

            <div class="setting-group">
                <label>Theme:</label>
                <div class="theme-toggle-container">
                    <button id="theme-toggle" class="theme-toggle-btn" data-theme="auto" aria-label="Toggle theme">
                        <div class="theme-icon">
                            <svg class="theme-svg" viewBox="0 0 24 24" width="20" height="20">
                                <!-- Auto icon (computer) -->
                                <path class="auto-icon" d="M4 6h16v10H4V6zm2 2v6h12V8H6zm-2 8h16v2H4v-2z" />
                                <!-- Light icon (sun) -->
                                <path class="light-icon"
                                    d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1z" />
                                <!-- Dark icon (moon) -->
                                <path class="dark-icon"
                                    d="M12 2.5c-.19 0-.38.01-.57.03a6.5 6.5 0 0 0 0 12.94c.19.02.38.03.57.03 5.52 0 10-4.48 10-10S17.52 2.5 12 2.5z" />
                            </svg>
                        </div>
                        <span class="theme-label">Auto</span>
                    </button>
                </div>
            </div>

            <div class="filter-group" id="disable-on-site-group">
                <label class="toggle-switch">
                    <input type="checkbox" id="disable-on-site">
                    <span class="slider"></span>
                </label>
                <label for="disable-on-site" id="disable-on-site-label">Enable on this website</label>
            </div>
        </div>

        <div id="elevenlabs-voice-settings">
            <h2>Voice Settings</h2>
            <div class="toggle-button" id="voice-filter-free-toggle" data-setting="voice-filter-free">
                <div class="toggle-indicator"></div>
                <span class="toggle-label">Show only free voices</span>
            </div>
            <label for="language-filter">Filter by Language:</label>
            <select id="language-filter">
                <option value="all">All Languages</option>
            </select>
            <label for="gender-filter">Filter by Gender:</label>
            <select id="gender-filter">
                <option value="all">All Genders</option>
            </select>
            <div class="setting-group">
                <label for="voice">Select a Voice:</label>
                <select id="voice"></select>
                <span class="voice-status" id="elevenlabs-voice-status"></span>
            </div>
            <div class="setting-group">
                <label for="speed-slider">Playback Speed: <span id="speed-value">1.0</span>x</label>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0.7x</span>
                    <input type="range" id="speed-slider" min="0.7" max="1.2" step="0.01" value="1">
                    <span style="margin-left: 10px;">1.2x</span>
                </div>

                <label for="stability-slider">Stability: <span id="stability-value">50</span>%</label>
                <p style="font-size: 12px; margin-top: -8px; margin-bottom: 8px;">Lower values are more emotional,
                    higher
                    are
                    more
                    stable.</p>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0%</span>
                    <input type="range" id="stability-slider" min="0" max="1" step="0.01" value="0.5">
                    <span style="margin-left: 10px;">100%</span>
                </div>

                <label for="similarity-slider">Similarity Boost: <span id="similarity-value">75</span>%</label>
                <p style="font-size: 12px; margin-top: -8px; margin-bottom: 8px;">Higher values are closer to the
                    original
                    voice.
                </p>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0%</span>
                    <input type="range" id="similarity-slider" min="0" max="1" step="0.01" value="0.75">
                    <span style="margin-left: 10px;">100%</span>
                </div>

                <label for="style-slider">Style Exaggeration: <span id="style-value">0</span>%</label>
                <p style="font-size: 12px; margin-top: -8px; margin-bottom: 8px;">Higher values exaggerate the voice's
                    style.
                    Best
                    kept low.</p>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0%</span>
                    <input type="range" id="style-slider" min="0" max="1" step="0.01" value="0">
                    <span style="margin-left: 10px;">100%</span>
                </div>
            </div>
        </div>

        <div id="hume-voice-settings" style="display: none;">
            <h2>Voice Settings</h2>
            <div class="setting-group">
                <label for="hume-voice">Select a Voice:</label>
                <select id="hume-voice"></select>
                <span class="voice-status" id="hume-voice-status"></span>
            </div>
            <div class="setting-group">
                <label for="hume-speed-slider">Playback Speed: <span id="hume-speed-value">1.0</span>x</label>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0.5x</span>
                    <input type="range" id="hume-speed-slider" min="0.5" max="2.0" step="0.01" value="1">
                    <span style="margin-left: 10px;">2.0x</span>
                </div>
            </div>
        </div>

        <div id="straico-voice-settings" style="display: none;">
            <h2>Voice Settings</h2>
            <label for="straico-model-select">Select a Model:</label>
            <select id="straico-model-select">
                <option value="eleven_multilingual_v2">ElevenLabs</option>
                <option value="tts-1">OpenAI</option>
            </select>
            <div class="setting-group">
                <label for="straico-voice">Select a Voice:</label>
                <select id="straico-voice"></select>
                <span class="voice-status" id="straico-voice-status"></span>
            </div>
            <div class="setting-group">
                <label for="straico-speed-slider">Playback Speed: <span id="straico-speed-value">1.0</span>x</label>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">0.5x</span>
                    <input type="range" id="straico-speed-slider" min="0.5" max="2.0" step="0.01" value="1">
                    <span style="margin-left: 10px;">2.0x</span>
                </div>
            </div>
        </div>
    </div>
    <div id="text-to-audio" class="tab-content">
        <h2>Text to Audio</h2>
        <div id="tta-info" style="font-size: 12px; margin-bottom: 8px; min-height: 60px;">
            <span id="tta-provider">Provider: </span><br>
            <span id="tta-model">Model: </span><br>
            <span id="tta-voice">Voice: </span><br>
            <span id="tta-credits">Credits: </span>
        </div>
        <div id="output-format-container" style="display: none;">
            <label for="output-format">Output Format:</label>
            <select id="output-format" style="margin-bottom: 8px;">
                <option value="mp3">MP3</option>
            </select>
        </div>
        <div class="textarea-container">
            <textarea id="text-to-audio-input" rows="4" style="width: 100%; box-sizing: border-box;"
                placeholder="Paste text here to convert to audio..."></textarea>
            <button id="clear-text-btn" title="Clear text">×</button>
        </div>
        <div id="text-stats" style="font-size: 12px; margin-bottom: 8px; color: var(--muted);">
            <span>Chars: 0</span> |
            <span>Words: 0</span> |
            <span>Time: 0s</span>
        </div>
        <button id="convert-btn">Convert and Download</button>
        <div id="download-container" style="margin-top: 8px;"></div>
    </div>

    <script src="popup.js" type="module"></script>
    <textarea id="clipboard-helper" style="position: absolute; top: -9999px; left: -9999px;"></textarea>
</body>

</html>