import { pcmToWav } from '../shared/utils.js';

export const google = {
    // Get current Pacific Time date string
    getPacificTimeDate() {
        const now = new Date();
        // Use Intl.DateTimeFormat for more reliable timezone conversion
        const pacificTime = new Intl.DateTimeFormat('en-CA', {
            timeZone: 'America/Los_Angeles',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).format(now);
        return pacificTime; // Returns YYYY-MM-DD format
    },

    // Get next midnight in Pacific Time for alarm scheduling
    getNextMidnightPacific() {
        const now = new Date();

        // Get current time parts in LA
        const parts = new Intl.DateTimeFormat('en-US', {
            timeZone: 'America/Los_Angeles',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
            hour12: false
        }).formatToParts(now).reduce((acc, part) => {
            acc[part.type] = parseInt(part.value, 10);
            return acc;
        }, {});

        // The hour can be 24 for midnight, normalize to 0
        if (parts.hour === 24) parts.hour = 0;

        // Calculate milliseconds remaining in the current day in LA
        const msPassedToday = (parts.hour * 3600 + parts.minute * 60 + parts.second) * 1000;
        const msInADay = 24 * 60 * 60 * 1000;
        const msRemaining = msInADay - msPassedToday;

        // The alarm should be set for now + remaining milliseconds, with a small buffer
        return now.getTime() + msRemaining + 5000; // Add 5s buffer
    },

    // Reset daily quota
    async resetDailyQuota() {
        const today = this.getPacificTimeDate();
        const { googleRequests } = await chrome.storage.local.get(['googleRequests']);
        let requests = googleRequests || {};

        // Clean up old data and ensure today's count is reset
        requests = await this.cleanupOldRequests(requests);

        // Explicitly reset today's count to 0
        requests[today] = 0;

        await chrome.storage.local.set({ googleRequests: requests });

        return true;
    },

    // Clean up old request data to prevent storage bloat
    async cleanupOldRequests(googleRequests) {
        const currentDate = this.getPacificTimeDate();
        const currentTime = new Date(currentDate).getTime();
        const oneDayMs = 24 * 60 * 60 * 1000;

        // Keep only last 7 days of data
        const cleanedRequests = {};
        for (const [date, count] of Object.entries(googleRequests)) {
            const dateTime = new Date(date).getTime();
            if (currentTime - dateTime <= 7 * oneDayMs) {
                cleanedRequests[date] = count;
            }
        }
        return cleanedRequests;
    },

    // Get current quota status
    async getQuotaStatus() {
        const today = this.getPacificTimeDate();
        const { googleRequests } = await chrome.storage.local.get(['googleRequests']);
        const requests = googleRequests || {};
        const todayCount = requests[today] || 0;

        return {
            date: today,
            count: todayCount,
            limit: 15,
            isOverQuota: todayCount >= 15,
            remaining: Math.max(0, 15 - todayCount)
        };
    },

    async process(text, settings) {
        if (!settings.googleApiKey) {
            throw new Error('Google API key is required. Please click the "🔑 Get API key" link in the extension popup to get your free Google API key.');
        }

        const model = "gemini-2.5-flash-preview-tts";
        const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${settings.googleApiKey}`;

        // Get Pacific Time date for proper quota tracking
        const today = this.getPacificTimeDate();
        const { googleRequests } = await chrome.storage.local.get(['googleRequests']);
        let requests = googleRequests || {};

        // Clean up old data
        requests = await this.cleanupOldRequests(requests);

        // Increment request counter
        requests[today] = (requests[today] || 0) + 1;
        await chrome.storage.local.set({ googleRequests: requests });

        // Log quota status for debugging
        const quotaStatus = await this.getQuotaStatus();


        const body = {
            contents: [{
                parts: [{
                    text: text
                }]
            }],
            generationConfig: {
                responseModalities: ["AUDIO"],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: settings.selectedGoogleVoice || "Kore"
                        }
                    }
                }
            },
            model: model,
        };



        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body),
        });

        if (!response.ok) {
            // Prevent console errors from showing in chrome://extensions
            let userMessage = '';

            try {
                const errorData = await response.json();

                // Prioritize the detailed error message from the API response
                if (errorData.error?.message) {
                    userMessage = errorData.error.message;
                }
                // Fallback to generic messages based on status code
                else if (response.status === 401 || response.status === 403) {
                    userMessage = 'Invalid Google API key. Please check your API key in the extension settings.';
                }
                // Handle rate limiting
                else if (response.status === 429) {
                    userMessage = 'Too many requests. Please wait a moment and try again.';
                }
                // Handle general API errors with user-friendly messages
                else if (response.status >= 400 && response.status < 500) {
                    userMessage = 'There was an issue with your request. Please check your settings and try again.';
                }
                else if (response.status >= 500) {
                    userMessage = 'Google TTS service is temporarily unavailable. Please try again later.';
                }
                else {
                    userMessage = 'An unexpected error occurred. Please try again.';
                }
            } catch (parseError) {
                // If we can't parse the error response, show a generic message
                userMessage = 'Google TTS service is temporarily unavailable. Please try again later.';
            }

            // Log the actual error for debugging but don't let it propagate to chrome://extensions


            // Throw a clean user-friendly error.
            throw new Error(userMessage);
        }

        const data = await response.json();

        // Check for valid response structure before proceeding
        if (!data.candidates || !data.candidates[0].content || !data.candidates[0].content.parts[0].inlineData) {
            // Handle cases where the API returns a 200 OK but no audio data
            throw new Error('Google TTS returned an invalid response. Please try again.');
        }

        const audioContent = data.candidates[0].content.parts[0].inlineData.data;
        const pcmData = Uint8Array.from(atob(audioContent), c => c.charCodeAt(0));
        const wavData = pcmToWav(pcmData, { sampleRate: 24000, channels: 1, bitsPerSample: 16 });

        const blob = new Blob([wavData], { type: 'audio/wav' });
        const dataUrl = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });

        return {
            audioUrl: dataUrl,
            alignment: null,
        };
    },
};
